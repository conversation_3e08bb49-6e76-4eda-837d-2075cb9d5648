{% extends 'base.html' %}
{% load static %}

{% block title %}الملف الشخصي - أوساريك{% endblock %}

{% block extra_css %}
<style>
    .profile-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 30px;
    }

    .profile-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 40px;
        border-radius: 20px;
        text-align: center;
        margin-bottom: 30px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .profile-avatar {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 48px;
        font-weight: bold;
        margin: 0 auto 20px;
        border: 4px solid rgba(255, 255, 255, 0.3);
    }

    .profile-name {
        font-size: 28px;
        font-weight: bold;
        margin-bottom: 10px;
    }

    .profile-role {
        font-size: 16px;
        opacity: 0.9;
        background: rgba(255, 255, 255, 0.2);
        padding: 8px 20px;
        border-radius: 20px;
        display: inline-block;
    }

    .profile-form {
        background: white;
        padding: 40px;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .form-section {
        margin-bottom: 30px;
    }

    .section-title {
        font-size: 20px;
        font-weight: bold;
        color: #333;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #f0f0f0;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .form-group {
        margin-bottom: 25px;
    }

    .form-label {
        display: block;
        font-weight: 600;
        color: #555;
        margin-bottom: 8px;
        font-size: 14px;
    }

    .form-control {
        width: 100%;
        padding: 15px;
        border: 2px solid #e0e0e0;
        border-radius: 10px;
        font-size: 16px;
        transition: all 0.3s ease;
        background: #f8f9fa;
    }

    .form-control:focus {
        outline: none;
        border-color: #667eea;
        background: white;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    .btn-update {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 15px 40px;
        border: none;
        border-radius: 10px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .btn-update:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
    }

    .btn-back {
        background: #6c757d;
        color: white;
        padding: 12px 30px;
        border: none;
        border-radius: 10px;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 20px;
    }

    .btn-back:hover {
        background: #5a6268;
        transform: translateY(-1px);
        color: white;
        text-decoration: none;
    }

    .info-card {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 10px;
        border-left: 4px solid #667eea;
        margin-bottom: 20px;
    }

    .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 0;
        border-bottom: 1px solid #e0e0e0;
    }

    .info-item:last-child {
        border-bottom: none;
    }

    .info-label {
        font-weight: 600;
        color: #555;
    }

    .info-value {
        color: #333;
        font-weight: 500;
    }

    @media (max-width: 768px) {
        .profile-container {
            padding: 15px;
        }
        
        .profile-header {
            padding: 30px 20px;
        }
        
        .profile-form {
            padding: 25px 20px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="profile-container">
    <!-- زر العودة -->
    <a href="{% url 'dashboard_home' %}" class="btn-back">
        <i class="bi bi-arrow-right"></i>
        العودة للوحة التحكم
    </a>

    <!-- رأس الملف الشخصي -->
    <div class="profile-header">
        <div class="profile-avatar">
            {{ user.first_name.0|default:user.username.0|upper }}{{ user.last_name.0|default:""|upper }}
        </div>
        <div class="profile-name">
            {{ user.get_full_name|default:user.username }}
        </div>
        <div class="profile-role">
            {% if user.is_superuser %}
                <i class="bi bi-shield-check"></i>
                مدير النظام
            {% elif user.is_staff %}
                <i class="bi bi-person-badge"></i>
                موظف
            {% else %}
                <i class="bi bi-person"></i>
                مستخدم
            {% endif %}
        </div>
    </div>

    <!-- معلومات الحساب -->
    <div class="profile-form">
        <div class="form-section">
            <div class="section-title">
                <i class="bi bi-info-circle"></i>
                معلومات الحساب
            </div>
            <div class="info-card">
                <div class="info-item">
                    <span class="info-label">اسم المستخدم:</span>
                    <span class="info-value">{{ user.username }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">تاريخ الانضمام:</span>
                    <span class="info-value">{{ user.date_joined|date:"d/m/Y" }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">آخر تسجيل دخول:</span>
                    <span class="info-value">
                        {% if user.last_login %}
                            {{ user.last_login|date:"d/m/Y H:i" }}
                        {% else %}
                            لم يسجل دخول من قبل
                        {% endif %}
                    </span>
                </div>
                <div class="info-item">
                    <span class="info-label">حالة الحساب:</span>
                    <span class="info-value">
                        {% if user.is_active %}
                            <span style="color: #28a745;">
                                <i class="bi bi-check-circle"></i>
                                نشط
                            </span>
                        {% else %}
                            <span style="color: #dc3545;">
                                <i class="bi bi-x-circle"></i>
                                غير نشط
                            </span>
                        {% endif %}
                    </span>
                </div>
            </div>
        </div>

        <!-- نموذج تحديث البيانات -->
        <form method="post">
            {% csrf_token %}
            <div class="form-section">
                <div class="section-title">
                    <i class="bi bi-person-lines-fill"></i>
                    البيانات الشخصية
                </div>
                
                <div class="form-group">
                    <label for="first_name" class="form-label">
                        <i class="bi bi-person"></i>
                        الاسم الأول *
                    </label>
                    <input type="text" 
                           id="first_name" 
                           name="first_name" 
                           class="form-control" 
                           value="{{ user.first_name }}" 
                           required>
                </div>

                <div class="form-group">
                    <label for="last_name" class="form-label">
                        <i class="bi bi-person"></i>
                        الاسم الأخير *
                    </label>
                    <input type="text" 
                           id="last_name" 
                           name="last_name" 
                           class="form-control" 
                           value="{{ user.last_name }}" 
                           required>
                </div>

                <div class="form-group">
                    <label for="email" class="form-label">
                        <i class="bi bi-envelope"></i>
                        البريد الإلكتروني *
                    </label>
                    <input type="email" 
                           id="email" 
                           name="email" 
                           class="form-control" 
                           value="{{ user.email }}" 
                           required>
                </div>

                <button type="submit" class="btn-update">
                    <i class="bi bi-check-circle"></i>
                    تحديث البيانات
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
