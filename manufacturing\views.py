from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse

@login_required
def manufacturing_dashboard(request):
    """لوحة تحكم التصنيع"""
    context = {
        'message': 'مرحباً بك في قسم التصنيع - قيد التطوير',
        'message_en': 'Welcome to Manufacturing Department - Under Development'
    }
    return render(request, 'manufacturing/dashboard.html', context)

@login_required
def production_order_list(request):
    """قائمة أوامر الإنتاج"""
    from dashboard.context_processors import user_settings
    user_context = user_settings(request)
    user_language = user_context.get('user_language', 'ar')

    if user_language == 'en':
        messages.info(request, 'This page is under development')
    else:
        messages.info(request, 'هذه الصفحة قيد التطوير')
    return render(request, 'manufacturing/dashboard.html', {})

@login_required
def production_order_create(request):
    """إنشاء أمر إنتاج"""
    messages.info(request, 'هذه الصفحة قيد التطوير')
    return render(request, 'manufacturing/dashboard.html', {})

@login_required
def production_order_detail(request, pk):
    """تفاصيل أمر الإنتاج"""
    messages.info(request, 'هذه الصفحة قيد التطوير')
    return render(request, 'manufacturing/dashboard.html', {})

@login_required
def get_material_info(request):
    """API للحصول على معلومات المواد"""
    return JsonResponse({'message': 'قيد التطوير'})

@login_required
def calculate_production_cost(request):
    """API لحساب تكلفة الإنتاج"""
    return JsonResponse({'message': 'قيد التطوير'})
