{% extends 'base.html' %}
{% load static %}

{% block title %}
    {% if action == 'create' %}{% if user_language == 'en' %}Add New Warehouse{% else %}إضافة مخزن جديد{% endif %}{% else %}{% if user_language == 'en' %}Edit Warehouse{% else %}تعديل المخزن{% endif %}{% endif %} - {% if user_language == 'en' %}Warehouse Definition{% else %}تعريف المخازن{% endif %}
{% endblock %}

{% block extra_css %}
<style>
    .warehouse-form-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 2rem 0;
        margin-bottom: 2rem;
        color: white;
        border-radius: 15px;
    }

    .form-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        padding: 2rem;
        margin-bottom: 2rem;
    }

    .form-section {
        margin-bottom: 2rem;
        padding-bottom: 1.5rem;
        border-bottom: 1px solid #eee;
    }

    .form-section:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }

    .section-title {
        color: #495057;
        font-weight: 600;
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid #667eea;
        display: inline-block;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 0.5rem;
    }

    .required {
        color: #dc3545;
    }

    .form-control, .form-select {
        border-radius: 8px;
        border: 2px solid #e2e8f0;
        padding: 0.75rem 1rem;
        transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    .form-check {
        margin-bottom: 1rem;
    }

    .form-check-input:checked {
        background-color: #667eea;
        border-color: #667eea;
    }

    .btn-save {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        padding: 0.75rem 2rem;
        border-radius: 8px;
        font-weight: 600;
        color: white;
        transition: all 0.3s ease;
    }

    .btn-save:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        color: white;
    }

    .help-text {
        font-size: 0.875rem;
        color: #6c757d;
        margin-top: 0.25rem;
    }

    .warehouse-type-info {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 1rem;
        margin-top: 0.5rem;
    }
</style>
{% endblock %}

{% block content %}
<!-- Form Header -->
<div class="warehouse-form-header">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="bi bi-{% if action == 'create' %}plus-circle{% else %}pencil{% endif %} me-2"></i>
                    {% if action == 'create' %}{% if user_language == 'en' %}Add New Warehouse{% else %}إضافة مخزن جديد{% endif %}{% else %}{% if user_language == 'en' %}Edit Warehouse{% else %}تعديل المخزن{% endif %}{% endif %}
                </h1>
                <p class="mb-0 opacity-75">
                    {% if action == 'create' %}{% if user_language == 'en' %}Create a new warehouse definition in the system{% else %}إنشاء تعريف مخزن جديد في النظام{% endif %}{% else %}{% if user_language == 'en' %}Update existing warehouse data{% else %}تحديث بيانات المخزن الموجود{% endif %}{% endif %}
                </p>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'definitions:warehouse_list' %}" class="btn btn-light">
                    <i class="bi bi-arrow-right me-1"></i>{% if user_language == 'en' %}Back to List{% else %}العودة للقائمة{% endif %}
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Form Content -->
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="form-card">
                <form method="post">
                    {% csrf_token %}
                    
                    <!-- Basic Information -->
                    <div class="form-section">
                        <h4 class="section-title">
                            <i class="bi bi-info-circle me-2"></i>{% if user_language == 'en' %}Basic Information{% else %}المعلومات الأساسية{% endif %}
                        </h4>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="code" class="form-label">
                                        {% if user_language == 'en' %}Warehouse Code{% else %}كود المخزن{% endif %} <span class="required">*</span>
                                    </label>
                                    <input type="text"
                                           id="code"
                                           name="code"
                                           class="form-control"
                                           value="{% if warehouse %}{{ warehouse.code }}{% elif form_data %}{{ form_data.code }}{% endif %}"
                                           required
                                           placeholder="{% if user_language == 'en' %}Example: WH001{% else %}مثال: WH001{% endif %}">
                                    <div class="help-text">{% if user_language == 'en' %}Unique warehouse code (letters and numbers only){% else %}كود فريد للمخزن (أحرف وأرقام فقط){% endif %}</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="name" class="form-label">
                                        {% if user_language == 'en' %}Warehouse Name{% else %}اسم المخزن{% endif %} <span class="required">*</span>
                                    </label>
                                    <input type="text"
                                           id="name"
                                           name="name"
                                           class="form-control"
                                           value="{% if warehouse %}{{ warehouse.name }}{% elif form_data %}{{ form_data.name }}{% endif %}"
                                           required
                                           placeholder="{% if user_language == 'en' %}Example: Main Warehouse{% else %}مثال: المخزن الرئيسي{% endif %}">
                                    <div class="help-text">{% if user_language == 'en' %}Full warehouse name{% else %}الاسم الكامل للمخزن{% endif %}</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="warehouse_type" class="form-label">
                                        {% if user_language == 'en' %}Warehouse Type{% else %}نوع المخزن{% endif %} <span class="required">*</span>
                                    </label>
                                    <select id="warehouse_type" name="warehouse_type" class="form-select" required>
                                        <option value="">{% if user_language == 'en' %}Select warehouse type{% else %}اختر نوع المخزن{% endif %}</option>
                                        {% for value, label in warehouse_types %}
                                            <option value="{{ value }}" 
                                                {% if warehouse and warehouse.warehouse_type == value %}selected{% endif %}
                                                {% if form_data and form_data.warehouse_type == value %}selected{% endif %}>
                                                {{ label }}
                                            </option>
                                        {% endfor %}
                                    </select>
                                    <div class="warehouse-type-info" id="typeInfo" style="display: none;">
                                        <small class="text-muted">{% if user_language == 'en' %}Selected type information will be displayed here{% else %}سيتم عرض معلومات النوع المختار هنا{% endif %}</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="manager_name" class="form-label">{% if user_language == 'en' %}Warehouse Manager Name{% else %}اسم مدير المخزن{% endif %}</label>
                                    <input type="text"
                                           id="manager_name"
                                           name="manager_name"
                                           class="form-control"
                                           value="{% if warehouse %}{{ warehouse.manager_name }}{% elif form_data %}{{ form_data.manager_name }}{% endif %}"
                                           placeholder="{% if user_language == 'en' %}Name of warehouse manager{% else %}اسم المسؤول عن المخزن{% endif %}">
                                    <div class="help-text">{% if user_language == 'en' %}Optional - Name of the person responsible for warehouse management{% else %}اختياري - اسم الشخص المسؤول عن إدارة المخزن{% endif %}</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Contact Information -->
                    <div class="form-section">
                        <h4 class="section-title">
                            <i class="bi bi-telephone me-2"></i>{% if user_language == 'en' %}Contact & Location Information{% else %}معلومات الاتصال والموقع{% endif %}
                        </h4>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="phone" class="form-label">{% if user_language == 'en' %}Phone Number{% else %}رقم الهاتف{% endif %}</label>
                                    <input type="tel"
                                           id="phone"
                                           name="phone"
                                           class="form-control"
                                           value="{% if warehouse %}{{ warehouse.phone }}{% elif form_data %}{{ form_data.phone }}{% endif %}"
                                           placeholder="{% if user_language == 'en' %}Example: 01234567890{% else %}مثال: 01234567890{% endif %}">
                                    <div class="help-text">{% if user_language == 'en' %}Warehouse or manager phone number{% else %}رقم هاتف المخزن أو المسؤول{% endif %}</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="address" class="form-label">{% if user_language == 'en' %}Address{% else %}العنوان{% endif %}</label>
                                    <input type="text"
                                           id="address"
                                           name="address"
                                           class="form-control"
                                           value="{% if warehouse %}{{ warehouse.address }}{% elif form_data %}{{ form_data.address }}{% endif %}"
                                           placeholder="{% if user_language == 'en' %}Full warehouse address{% else %}العنوان الكامل للمخزن{% endif %}">
                                    <div class="help-text">{% if user_language == 'en' %}Geographical location of warehouse{% else %}الموقع الجغرافي للمخزن{% endif %}</div>
                                </div>
                            </div>
                        </div>

                        <!-- {% if user_language == 'en' %}Description field not available in current form{% else %}حقل الوصف غير متوفر في النموذج الحالي{% endif %} -->
                    </div>

                    <!-- Settings -->
                    <div class="form-section">
                        <h4 class="section-title">
                            <i class="bi bi-gear me-2"></i>{% if user_language == 'en' %}Warehouse Settings{% else %}إعدادات المخزن{% endif %}
                        </h4>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input type="checkbox" 
                                           id="is_active" 
                                           name="is_active" 
                                           class="form-check-input"
                                           {% if not warehouse or warehouse.is_active %}checked{% endif %}
                                           {% if form_data and form_data.is_active %}checked{% endif %}>
                                    <label for="is_active" class="form-check-label">
                                        <strong>{% if user_language == 'en' %}Active Warehouse{% else %}مخزن نشط{% endif %}</strong>
                                    </label>
                                    <div class="help-text">{% if user_language == 'en' %}Warehouse can be used in operations{% else %}يمكن استخدام المخزن في العمليات{% endif %}</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input type="checkbox" 
                                           id="allow_negative_stock" 
                                           name="allow_negative_stock" 
                                           class="form-check-input"
                                           {% if warehouse and warehouse.allow_negative_stock %}checked{% endif %}
                                           {% if form_data and form_data.allow_negative_stock %}checked{% endif %}>
                                    <label for="allow_negative_stock" class="form-check-label">
                                        <strong>السماح بالمخزون السالب</strong>
                                    </label>
                                    <div class="help-text">إمكانية صرف أكثر من المتوفر</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input type="checkbox" 
                                           id="auto_reorder" 
                                           name="auto_reorder" 
                                           class="form-check-input"
                                           {% if warehouse and warehouse.auto_reorder %}checked{% endif %}
                                           {% if form_data and form_data.auto_reorder %}checked{% endif %}>
                                    <label for="auto_reorder" class="form-check-label">
                                        <strong>إعادة الطلب التلقائي</strong>
                                    </label>
                                    <div class="help-text">تنبيهات عند نفاد المخزون</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="text-center">
                        <button type="submit" class="btn btn-save btn-lg me-3">
                            <i class="bi bi-{% if action == 'create' %}plus-circle{% else %}check-circle{% endif %} me-2"></i>
                            {% if action == 'create' %}إنشاء المخزن{% else %}حفظ التغييرات{% endif %}
                        </button>
                        <a href="{% url 'definitions:warehouse_list' %}" class="btn btn-outline-secondary btn-lg">
                            <i class="bi bi-x-circle me-2"></i>إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Help Section -->
    <div class="row">
        <div class="col-12">
            <div class="alert alert-info">
                <h6><i class="bi bi-info-circle me-2"></i>معلومات مهمة:</h6>
                <ul class="mb-0">
                    <li><strong>كود المخزن:</strong> يجب أن يكون فريداً ولا يمكن تغييره بعد الإنشاء</li>
                    <li><strong>نوع المخزن:</strong> يحدد طبيعة استخدام المخزن (رئيسي، مواد خام، منتجات تامة، إلخ)</li>
                    <li><strong>المخزون السالب:</strong> إذا تم تفعيله، يمكن صرف كميات أكبر من المتوفر</li>
                    <li><strong>إعادة الطلب التلقائي:</strong> ينشئ تنبيهات عندما ينخفض المخزون عن الحد الأدنى</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // معلومات أنواع المخازن
    const warehouseTypeInfo = {
        {% if user_language == 'en' %}
            'main': 'Main Warehouse - Used to store all types of goods and products',
            'raw_materials': 'Raw Materials Warehouse - Dedicated to raw materials used in production',
            'finished_goods': 'Finished Goods Warehouse - For products ready for sale',
            'damaged': 'Damaged Warehouse - For damaged or returned goods',
            'quarantine': 'Quarantine Warehouse - For goods under inspection or review',
            'transit': 'Transit Warehouse - For goods on their way to other destinations'
        {% else %}
            'main': 'المخزن الرئيسي - يستخدم لتخزين جميع أنواع البضائع والمنتجات',
            'raw_materials': 'مخزن المواد الخام - مخصص للمواد الأولية المستخدمة في الإنتاج',
            'finished_goods': 'مخزن المنتجات التامة - للمنتجات الجاهزة للبيع',
            'damaged': 'مخزن التالف - للبضائع التالفة أو المرتجعة',
            'quarantine': 'مخزن الحجر - للبضائع تحت الفحص أو المراجعة',
            'transit': 'مخزن العبور - للبضائع في طريقها لوجهات أخرى'
        {% endif %}
    };
    
    const warehouseTypeSelect = document.getElementById('warehouse_type');
    const typeInfo = document.getElementById('typeInfo');
    
    warehouseTypeSelect.addEventListener('change', function() {
        const selectedType = this.value;
        if (selectedType && warehouseTypeInfo[selectedType]) {
            typeInfo.innerHTML = '<small class="text-info"><i class="bi bi-info-circle me-1"></i>' + warehouseTypeInfo[selectedType] + '</small>';
            typeInfo.style.display = 'block';
        } else {
            typeInfo.style.display = 'none';
        }
    });
    
    // تشغيل الدالة عند تحميل الصفحة إذا كان هناك نوع محدد
    if (warehouseTypeSelect.value) {
        warehouseTypeSelect.dispatchEvent(new Event('change'));
    }
    
    // التحقق من صحة الكود
    const codeInput = document.getElementById('code');
    codeInput.addEventListener('input', function() {
        // إزالة المسافات والأحرف الخاصة
        this.value = this.value.replace(/[^a-zA-Z0-9]/g, '').toUpperCase();
    });
});
</script>
{% endblock %}
