<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>البحث الذكي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .search-container {
            padding-top: 3rem;
        }
        
        .search-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .search-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .search-box {
            position: relative;
            margin-bottom: 2rem;
        }
        
        .search-input {
            width: 100%;
            padding: 1rem 1rem 1rem 3rem;
            border: 2px solid #e5e7eb;
            border-radius: 15px;
            font-size: 1.1rem;
            transition: all 0.3s ease;
        }
        
        .search-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .search-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #6b7280;
            font-size: 1.2rem;
        }
        
        .result-item {
            border: 1px solid #e5e7eb;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .result-item:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        
        .result-icon {
            width: 50px;
            height: 50px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.3rem;
        }
        
        .result-content {
            flex: 1;
        }
        
        .result-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 0.5rem;
        }
        
        .result-title a {
            color: inherit;
            text-decoration: none;
        }
        
        .result-title a:hover {
            color: #667eea;
        }
        
        .result-description {
            color: #6b7280;
            line-height: 1.6;
        }
        
        .result-category {
            background: #667eea;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .stats-info {
            color: #6b7280;
            margin-bottom: 1.5rem;
            text-align: center;
        }
        
        .no-results {
            text-align: center;
            padding: 3rem;
            color: #6b7280;
        }
    </style>
</head>
<body>
    <div class="container search-container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="search-card">
                    <div class="search-header">
                        <h1><i class="bi bi-search me-3"></i>البحث الذكي</h1>
                        <p class="text-muted">ابحث في جميع أجزاء النظام</p>
                    </div>
                    
                    <div class="search-box">
                        <i class="bi bi-search search-icon"></i>
                        <input type="text" class="search-input" 
                               value="{{ query }}" 
                               placeholder="ابحث عن المنتجات، العملاء، الفواتير..."
                               onkeyup="if(event.key==='Enter') searchPage(this.value)">
                    </div>
                    
                    {% if query %}
                    <div class="stats-info">
                        <strong>{{ total_count }}</strong> نتيجة للبحث عن "<strong>{{ query }}</strong>"
                        {% if execution_time %}في {{ execution_time }} ثانية{% endif %}
                    </div>
                    {% endif %}
                    
                    <div class="search-results">
                        {% if results %}
                            {% for result in results %}
                            <div class="result-item">
                                <div class="result-icon" style="background: {% if result.icon == 'bi-box' %}#f59e0b{% elif result.icon == 'bi-people' %}#10b981{% elif result.icon == 'bi-receipt' %}#3b82f6{% elif result.icon == 'bi-person' %}#8b5cf6{% elif result.icon == 'bi-envelope' %}#06b6d4{% else %}#6b7280{% endif %};">
                                    <i class="{{ result.icon }}"></i>
                                </div>
                                <div class="result-content">
                                    <div class="result-title">
                                        <a href="{{ result.url }}">{{ result.title }}</a>
                                    </div>
                                    <div class="result-description">{{ result.description }}</div>
                                </div>
                                <div class="result-category">{{ result.category }}</div>
                            </div>
                            {% endfor %}
                        {% else %}
                            {% if query %}
                            <div class="no-results">
                                <i class="bi bi-search" style="font-size: 3rem; margin-bottom: 1rem; display: block; color: #d1d5db;"></i>
                                <h4>لا توجد نتائج</h4>
                                <p>لم نجد أي نتائج للبحث عن "{{ query }}"</p>
                                <p>جرب كلمات أخرى أو تأكد من الإملاء</p>
                            </div>
                            {% else %}
                            <div class="no-results">
                                <i class="bi bi-lightbulb" style="font-size: 3rem; margin-bottom: 1rem; display: block; color: #fbbf24;"></i>
                                <h4>نصائح للبحث</h4>
                                <div class="row mt-4">
                                    <div class="col-md-6">
                                        <h6>جرب البحث عن:</h6>
                                        <ul class="list-unstyled">
                                            <li>• لابتوب</li>
                                            <li>• أحمد محمد</li>
                                            <li>• فاتورة</li>
                                            <li>• طابعة</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>أو ابحث في:</h6>
                                        <ul class="list-unstyled">
                                            <li>• المنتجات</li>
                                            <li>• العملاء</li>
                                            <li>• الفواتير</li>
                                            <li>• المستخدمين</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                        {% endif %}
                    </div>
                    
                    <div class="text-center mt-4">
                        <a href="/" class="btn btn-secondary">
                            <i class="bi bi-house"></i> العودة للرئيسية
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function searchPage(query) {
            if (query.trim()) {
                window.location.href = `?q=${encodeURIComponent(query.trim())}`;
            }
        }
        
        // تركيز على مربع البحث عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.querySelector('.search-input');
            if (searchInput && !searchInput.value) {
                searchInput.focus();
            }
        });
    </script>
</body>
</html>
