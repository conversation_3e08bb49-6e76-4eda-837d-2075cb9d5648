{% extends 'base.html' %}
{% load static %}

{% block title %}إضافة مخزون - إذن إدخال{% endblock %}

{% block extra_css %}
<style>
    .add-stock-header {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        padding: 2rem 0;
        margin-bottom: 2rem;
        color: white;
        border-radius: 15px;
    }

    .form-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        padding: 2rem;
        margin-bottom: 2rem;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 0.5rem;
    }

    .form-control, .form-select {
        border-radius: 8px;
        border: 2px solid #e2e8f0;
        padding: 0.75rem 1rem;
        transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: #28a745;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    }

    .btn-add-stock {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
        padding: 0.75rem 2rem;
        border-radius: 8px;
        font-weight: 600;
        color: white;
        transition: all 0.3s ease;
    }

    .btn-add-stock:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        color: white;
    }

    .reason-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 500;
        background: #d4edda;
        color: #155724;
        margin-left: 0.5rem;
    }
</style>
{% endblock %}

{% block content %}
<!-- Add Stock Header -->
<div class="add-stock-header">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="bi bi-plus-circle me-2"></i>
                    إضافة مخزون - إذن إدخال
                </h1>
                <p class="mb-0 opacity-75">إضافة كميات جديدة للمخزون</p>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'warehouses:dashboard' %}" class="btn btn-light">
                    <i class="bi bi-arrow-right me-1"></i>العودة للمخازن
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Add Stock Form -->
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="form-card">
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="warehouse" class="form-label">المخزن</label>
                                <select name="warehouse" id="warehouse" class="form-select" required>
                                    <option value="">اختر المخزن</option>
                                    {% for warehouse in warehouses %}
                                        <option value="{{ warehouse.id }}">{{ warehouse.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="product" class="form-label">الصنف</label>
                                <select name="product" id="product" class="form-select" required>
                                    <option value="">اختر الصنف</option>
                                    {% for product in products %}
                                        <option value="{{ product.id }}">{{ product.name }} ({{ product.code }})</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="quantity" class="form-label">الكمية</label>
                                <input type="number" name="quantity" id="quantity" class="form-control" 
                                       step="0.001" min="0" required placeholder="أدخل الكمية">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="unit_cost" class="form-label">تكلفة الوحدة (ج.م)</label>
                                <input type="number" name="unit_cost" id="unit_cost" class="form-control" 
                                       step="0.01" min="0" required placeholder="أدخل تكلفة الوحدة">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="reason" class="form-label">سبب الإضافة</label>
                                <select name="reason" id="reason" class="form-select" required>
                                    <option value="purchase">شراء <span class="reason-badge">الأكثر شيوعاً</span></option>
                                    <option value="return_in">مرتجع وارد</option>
                                    <option value="production">إنتاج</option>
                                    <option value="adjustment">تسوية جرد</option>
                                    <option value="initial">رصيد افتتاحي</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="reference_number" class="form-label">رقم المرجع (اختياري)</label>
                                <input type="text" name="reference_number" id="reference_number" class="form-control" 
                                       placeholder="رقم الفاتورة أو المرجع">
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="notes" class="form-label">ملاحظات (اختياري)</label>
                        <textarea name="notes" id="notes" class="form-control" rows="3" 
                                  placeholder="أي ملاحظات إضافية..."></textarea>
                    </div>

                    <div class="text-center">
                        <button type="submit" class="btn btn-add-stock btn-lg">
                            <i class="bi bi-plus-circle me-2"></i>
                            إضافة للمخزون
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Quick Info -->
    <div class="row">
        <div class="col-12">
            <div class="alert alert-info">
                <h6><i class="bi bi-info-circle me-2"></i>معلومات مهمة:</h6>
                <ul class="mb-0">
                    <li>سيتم إنشاء رقم حركة تلقائي للعملية</li>
                    <li>سيتم تحديث رصيد المخزون فوراً بعد الحفظ</li>
                    <li>تأكد من صحة البيانات قبل الحفظ</li>
                    <li>يمكن مراجعة الحركة من قائمة حركات المخزون</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // حساب إجمالي التكلفة تلقائياً
    const quantityInput = document.getElementById('quantity');
    const unitCostInput = document.getElementById('unit_cost');
    
    function calculateTotal() {
        const quantity = parseFloat(quantityInput.value) || 0;
        const unitCost = parseFloat(unitCostInput.value) || 0;
        const total = quantity * unitCost;
        
        // يمكن إضافة عرض الإجمالي هنا
        {% if user_language == 'en' %}
            console.log('Total Cost:', total);
        {% else %}
            console.log('إجمالي التكلفة:', total);
        {% endif %}
    }
    
    quantityInput.addEventListener('input', calculateTotal);
    unitCostInput.addEventListener('input', calculateTotal);
});
</script>
{% endblock %}
