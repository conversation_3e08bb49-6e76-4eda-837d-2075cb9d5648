from django import forms
from django.contrib.auth.models import User
from .models import SystemSetting, UserPreference, BackupSchedule

class SystemSettingForm(forms.ModelForm):
    """نموذج إضافة/تعديل إعدادات النظام"""
    class Meta:
        model = SystemSetting
        fields = ['category', 'key', 'name', 'description', 'setting_type', 'value', 
                 'default_value', 'is_required', 'is_editable', 'order']
        widgets = {
            'category': forms.Select(attrs={'class': 'form-select'}),
            'key': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'مفتاح الإعداد'}),
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اسم الإعداد'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'وصف الإعداد'}),
            'setting_type': forms.Select(attrs={'class': 'form-select'}),
            'value': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'القيمة'}),
            'default_value': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'القيمة الافتراضية'}),
            'is_required': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'is_editable': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'order': forms.NumberInput(attrs={'class': 'form-control', 'min': '0'}),
        }

class UserPreferenceForm(forms.ModelForm):
    """نموذج تفضيلات المستخدم"""
    class Meta:
        model = UserPreference
        fields = ['preference_type', 'key', 'value']
        widgets = {
            'preference_type': forms.Select(attrs={'class': 'form-select'}),
            'key': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'مفتاح التفضيل'}),
            'value': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'القيمة'}),
        }

class BackupScheduleForm(forms.ModelForm):
    """نموذج جدولة النسخ الاحتياطية"""
    class Meta:
        model = BackupSchedule
        fields = ['name', 'description', 'frequency', 'backup_time', 'include_database', 
                 'include_media', 'retention_days', 'status']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اسم الجدولة'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'وصف الجدولة'}),
            'frequency': forms.Select(attrs={'class': 'form-select'}),
            'backup_time': forms.TimeInput(attrs={'class': 'form-control', 'type': 'time'}),
            'include_database': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'include_media': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'retention_days': forms.NumberInput(attrs={'class': 'form-control', 'min': '1'}),
            'status': forms.Select(attrs={'class': 'form-select'}),
        }

class CompanySettingsForm(forms.Form):
    """نموذج إعدادات الشركة"""
    company_name = forms.CharField(
        max_length=200,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اسم الشركة'})
    )
    company_address = forms.CharField(
        widget=forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'عنوان الشركة'})
    )
    company_phone = forms.CharField(
        max_length=20,
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'هاتف الشركة'})
    )
    company_email = forms.EmailField(
        required=False,
        widget=forms.EmailInput(attrs={'class': 'form-control', 'placeholder': 'بريد الشركة الإلكتروني'})
    )
    company_website = forms.URLField(
        required=False,
        widget=forms.URLInput(attrs={'class': 'form-control', 'placeholder': 'موقع الشركة الإلكتروني'})
    )
    tax_number = forms.CharField(
        max_length=50,
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'الرقم الضريبي'})
    )
    commercial_register = forms.CharField(
        max_length=50,
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'السجل التجاري'})
    )

class FinancialSettingsForm(forms.Form):
    """نموذج الإعدادات المالية"""
    default_currency = forms.CharField(
        max_length=3,
        initial='SAR',
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'العملة الافتراضية'})
    )
    decimal_places = forms.IntegerField(
        min_value=0,
        max_value=4,
        initial=2,
        widget=forms.NumberInput(attrs={'class': 'form-control'})
    )
    tax_rate = forms.DecimalField(
        max_digits=5,
        decimal_places=2,
        initial=15.00,
        widget=forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'})
    )
    fiscal_year_start = forms.DateField(
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'})
    )

class SecuritySettingsForm(forms.Form):
    """نموذج إعدادات الأمان"""
    password_min_length = forms.IntegerField(
        min_value=6,
        max_value=20,
        initial=8,
        widget=forms.NumberInput(attrs={'class': 'form-control'})
    )
    password_require_uppercase = forms.BooleanField(
        required=False,
        initial=True,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'})
    )
    password_require_lowercase = forms.BooleanField(
        required=False,
        initial=True,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'})
    )
    password_require_numbers = forms.BooleanField(
        required=False,
        initial=True,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'})
    )
    password_require_symbols = forms.BooleanField(
        required=False,
        initial=False,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'})
    )
    session_timeout = forms.IntegerField(
        min_value=15,
        max_value=480,
        initial=60,
        widget=forms.NumberInput(attrs={'class': 'form-control'})
    )
    max_login_attempts = forms.IntegerField(
        min_value=3,
        max_value=10,
        initial=5,
        widget=forms.NumberInput(attrs={'class': 'form-control'})
    )

class NotificationSettingsForm(forms.Form):
    """نموذج إعدادات الإشعارات"""
    email_notifications = forms.BooleanField(
        required=False,
        initial=True,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'})
    )
    sms_notifications = forms.BooleanField(
        required=False,
        initial=False,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'})
    )
    system_notifications = forms.BooleanField(
        required=False,
        initial=True,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'})
    )
    low_stock_alerts = forms.BooleanField(
        required=False,
        initial=True,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'})
    )
    payment_reminders = forms.BooleanField(
        required=False,
        initial=True,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'})
    )

class SettingsSearchForm(forms.Form):
    """نموذج البحث في الإعدادات"""
    search = forms.CharField(
        max_length=200,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'البحث بالاسم أو المفتاح...',
            'data-placeholder-en': 'Search by name or key...'
        })
    )
    category = forms.ChoiceField(
        choices=[('', 'جميع الفئات')] + SystemSetting.CATEGORIES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    setting_type = forms.ChoiceField(
        choices=[('', 'جميع الأنواع')] + SystemSetting.SETTING_TYPES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    is_editable = forms.ChoiceField(
        choices=[('', 'الكل'), ('True', 'قابل للتعديل'), ('False', 'غير قابل للتعديل')],
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
