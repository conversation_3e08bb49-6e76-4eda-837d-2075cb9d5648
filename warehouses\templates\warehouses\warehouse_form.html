{% extends 'base.html' %}

{% block title %}{{ title }} - أوساريك{% endblock %}

{% block content %}
    <div class="page-header">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'dashboard_home' %}">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="{% url 'warehouses:dashboard' %}">المخازن</a></li>
                <li class="breadcrumb-item"><a href="{% url 'warehouses:warehouse_list' %}">قائمة المخازن</a></li>
                <li class="breadcrumb-item active">{{ title }}</li>
            </ol>
        </nav>
        <h1 class="page-title">{{ title }}</h1>
        <p class="page-subtitle">
            {% if warehouse %}
                تعديل بيانات المخزن في النظام
            {% else %}
                إضافة مخزن جديد إلى النظام
            {% endif %}
        </p>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-building me-2"></i>بيانات المخزن
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-8 mb-3">
                                <label for="{{ form.name.id_for_label }}" class="form-label">
                                    <i class="bi bi-building me-1"></i>{{ form.name.label }}
                                    <span class="text-danger">*</span>
                                </label>
                                {{ form.name }}
                                {% if form.name.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.name.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="{{ form.code.id_for_label }}" class="form-label">
                                    <i class="bi bi-code me-1"></i>{{ form.code.label }}
                                    <span class="text-danger">*</span>
                                </label>
                                {{ form.code }}
                                {% if form.code.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.code.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-text">رمز فريد للمخزن</div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.warehouse_type.id_for_label }}" class="form-label">
                                    <i class="bi bi-list me-1"></i>{{ form.warehouse_type.label }}
                                    <span class="text-danger">*</span>
                                </label>
                                {{ form.warehouse_type }}
                                {% if form.warehouse_type.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.warehouse_type.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.branch.id_for_label }}" class="form-label">
                                    <i class="bi bi-geo-alt me-1"></i>{{ form.branch.label }}
                                    <span class="text-danger">*</span>
                                </label>
                                {{ form.branch }}
                                {% if form.branch.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.branch.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-8 mb-3">
                                <label for="{{ form.address.id_for_label }}" class="form-label">
                                    <i class="bi bi-geo-alt me-1"></i>{{ form.address.label }}
                                    <span class="text-danger">*</span>
                                </label>
                                {{ form.address }}
                                {% if form.address.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.address.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="{{ form.capacity.id_for_label }}" class="form-label">
                                    <i class="bi bi-box me-1"></i>{{ form.capacity.label }}
                                </label>
                                {{ form.capacity }}
                                {% if form.capacity.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.capacity.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-text">السعة بالمتر المكعب</div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="{{ form.manager_name.id_for_label }}" class="form-label">
                                    <i class="bi bi-person me-1"></i>{{ form.manager_name.label }}
                                </label>
                                {{ form.manager_name }}
                                {% if form.manager_name.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.manager_name.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="{{ form.phone.id_for_label }}" class="form-label">
                                    <i class="bi bi-telephone me-1"></i>{{ form.phone.label }}
                                </label>
                                {{ form.phone }}
                                {% if form.phone.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.phone.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="{{ form.email.id_for_label }}" class="form-label">
                                    <i class="bi bi-envelope me-1"></i>{{ form.email.label }}
                                </label>
                                {{ form.email }}
                                {% if form.email.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.email.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-8 mb-3">
                                <label for="{{ form.notes.id_for_label }}" class="form-label">
                                    <i class="bi bi-chat-text me-1"></i>{{ form.notes.label }}
                                </label>
                                {{ form.notes }}
                                {% if form.notes.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.notes.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label class="form-label">الإعدادات</label>
                                <div class="form-check">
                                    {{ form.is_active }}
                                    <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                        {{ form.is_active.label }}
                                    </label>
                                    {% if form.is_active.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.is_active.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{% url 'warehouses:warehouse_list' %}" class="btn btn-secondary">
                                <i class="bi bi-arrow-right me-2"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-lg me-2"></i>{{ button_text }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            {% if warehouse %}
                <!-- Warehouse Information -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-info-circle me-2"></i>معلومات المخزن
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-3">
                                <div class="border rounded p-3">
                                    <i class="bi bi-building text-primary" style="font-size: 2rem;"></i>
                                    <h4 class="mt-2 mb-1">{{ warehouse.code }}</h4>
                                    <p class="text-muted mb-0">رمز المخزن</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="border rounded p-3">
                                    <i class="bi bi-list text-success" style="font-size: 2rem;"></i>
                                    <h4 class="mt-2 mb-1">{{ warehouse.get_warehouse_type_display }}</h4>
                                    <p class="text-muted mb-0">نوع المخزن</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="border rounded p-3">
                                    <i class="bi bi-box text-info" style="font-size: 2rem;"></i>
                                    <h4 class="mt-2 mb-1">
                                        {% if warehouse.capacity %}
                                            {{ warehouse.capacity }} م³
                                        {% else %}
                                            غير محدد
                                        {% endif %}
                                    </h4>
                                    <p class="text-muted mb-0">السعة</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="border rounded p-3">
                                    <i class="bi bi-calendar-event text-warning" style="font-size: 2rem;"></i>
                                    <h4 class="mt-2 mb-1">{{ warehouse.created_at|date:"Y/m/d" }}</h4>
                                    <p class="text-muted mb-0">تاريخ الإضافة</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
{% endblock %}

{% block extra_css %}
<style>
    .form-label {
        font-weight: 600;
        color: #495057;
    }
    
    .form-control:focus {
        border-color: #0d6efd;
        box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    }
    
    .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
    }
    
    .breadcrumb {
        background-color: transparent;
        padding: 0;
        margin-bottom: 1rem;
    }
    
    .breadcrumb-item + .breadcrumb-item::before {
        content: "←";
    }
    
    .form-check {
        margin-bottom: 0.5rem;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    // Form validation and enhancements
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.querySelector('form');
        const codeInput = document.getElementById('{{ form.code.id_for_label }}');
        
        // Auto-uppercase warehouse code
        codeInput.addEventListener('input', function() {
            this.value = this.value.toUpperCase();
        });
        
        // Form submission
        form.addEventListener('submit', function() {
            const submitBtn = form.querySelector('button[type="submit"]');
            {% if user_language == 'en' %}
                submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Saving...';
            {% else %}
                submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>جاري الحفظ...';
            {% endif %}
            submitBtn.disabled = true;
        });
    });
</script>
{% endblock %}
