from django.urls import path
from . import views
from django.http import HttpResponse

app_name = 'warehouses'

# دوال مؤقتة للصفحات المعطلة
def temp_view(request):
    from dashboard.context_processors import user_settings
    user_context = user_settings(request)
    user_language = user_context.get('user_language', 'ar')

    if user_language == 'en':
        message = "This page is under development - will be activated soon"
    else:
        message = "هذه الصفحة قيد التطوير - سيتم تفعيلها قريباً"

    return HttpResponse(message)

urlpatterns = [
    # لوحة التحكم الرئيسية
    path('', views.warehouses_dashboard, name='dashboard'),

    # صفحات مؤقتة للروابط المطلوبة في template
    path('add-stock/', temp_view, name='add_stock'),
    path('remove-stock/', temp_view, name='remove_stock'),
    path('inventory/', temp_view, name='inventory_list'),
    path('transactions/', temp_view, name='transactions_list'),
    path('low-stock/', temp_view, name='low_stock_report'),
    path('adjustments/', temp_view, name='adjustments_list'),
    path('transfer/', temp_view, name='transfer_stock'),
]
