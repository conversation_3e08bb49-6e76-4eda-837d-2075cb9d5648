from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Q, Count, F
from django.db import models
from django.core.paginator import Paginator
from django.http import JsonResponse
from .models import (
    WarehouseDefinition, ProductDefinition, ProductCategory,
    CurrencyDefinition, BankDefinition, CashBoxDefinition,
    PersonDefinition, WarehouseLocation, UnitDefinition, AssetGroup, AssetBrand,
    ExpenseType, ExpenseName, RevenueType, RevenueName, ProfitCenter, PrinterDefinition
)

@login_required
def definitions_dashboard(request):
    """لوحة تحكم التعريفات"""
    # إحصائيات التعريفات
    from .models import ProductCode
    stats = {
        'warehouses': WarehouseDefinition.objects.count(),
        'warehouse_locations': WarehouseLocation.objects.count(),
        'products': ProductDefinition.objects.count(),
        'categories': ProductCategory.objects.count(),
        'product_codes': ProductCode.objects.count(),
        'persons': PersonDefinition.objects.count(),
        'customers': PersonDefinition.objects.filter(person_type='customer').count(),
        'suppliers': PersonDefinition.objects.filter(person_type='supplier').count(),
        'employees': PersonDefinition.objects.filter(person_type='employee').count(),
        'currencies': CurrencyDefinition.objects.count(),
        'banks': BankDefinition.objects.count(),
        'cashboxes': CashBoxDefinition.objects.count(),
        'asset_groups': AssetGroup.objects.count(),
        'asset_brands': AssetBrand.objects.count(),
        'expense_types': ExpenseType.objects.count(),
        'expense_names': ExpenseName.objects.count(),
        'revenue_types': RevenueType.objects.count(),
        'revenue_names': RevenueName.objects.count(),
        'profit_centers': ProfitCenter.objects.count(),
        'printers': PrinterDefinition.objects.count(),
        'units': UnitDefinition.objects.count(),
    }

    # إحصائيات إضافية
    recent_definitions = {
        'recent_products': ProductDefinition.objects.filter(is_active=True).order_by('-created_at')[:5],
        'recent_persons': PersonDefinition.objects.filter(is_active=True).order_by('-created_at')[:5],
        'recent_warehouses': WarehouseDefinition.objects.filter(is_active=True).order_by('-created_at')[:5],
    }

    context = {
        'stats': stats,
        'recent_definitions': recent_definitions,
    }

    return render(request, 'definitions/dashboard.html', context)


@login_required
def comprehensive_report(request):
    """التقرير الشامل لجميع التعريفات"""

    # إحصائيات شاملة
    stats = {
        # المخازن والأصناف
        'warehouses': WarehouseDefinition.objects.count(),
        'active_warehouses': WarehouseDefinition.objects.filter(is_active=True).count(),
        'products': ProductDefinition.objects.count(),
        'active_products': ProductDefinition.objects.filter(is_active=True).count(),
        'categories': ProductCategory.objects.count(),
        'active_categories': ProductCategory.objects.filter(is_active=True).count(),

        # العملات والبنوك
        'currencies': CurrencyDefinition.objects.count(),
        'active_currencies': CurrencyDefinition.objects.filter(is_active=True).count(),
        'banks': BankDefinition.objects.count(),
        'active_banks': BankDefinition.objects.filter(is_active=True).count(),
        'cash_boxes': CashBoxDefinition.objects.count(),
        'active_cash_boxes': CashBoxDefinition.objects.filter(is_active=True).count(),

        # الأشخاص
        'persons': PersonDefinition.objects.count(),
        'active_persons': PersonDefinition.objects.filter(is_active=True).count(),
        'customers': PersonDefinition.objects.filter(person_type='customer').count(),
        'suppliers': PersonDefinition.objects.filter(person_type='supplier').count(),
        'employees': PersonDefinition.objects.filter(person_type='employee').count(),

        # الوحدات والأصول
        'units': UnitDefinition.objects.count(),
        'active_units': UnitDefinition.objects.filter(is_active=True).count(),
        'asset_groups': AssetGroup.objects.count(),
        'active_asset_groups': AssetGroup.objects.filter(is_active=True).count(),
        'asset_brands': AssetBrand.objects.count(),
        'active_asset_brands': AssetBrand.objects.filter(is_active=True).count(),

        # المصروفات والإيرادات
        'expense_types': ExpenseType.objects.count(),
        'active_expense_types': ExpenseType.objects.filter(is_active=True).count(),
        'expense_names': ExpenseName.objects.count(),
        'active_expense_names': ExpenseName.objects.filter(is_active=True).count(),
        'revenue_types': RevenueType.objects.count(),
        'active_revenue_types': RevenueType.objects.filter(is_active=True).count(),
        'revenue_names': RevenueName.objects.count(),
        'active_revenue_names': RevenueName.objects.filter(is_active=True).count(),

        # مراكز الربحية والطابعات
        'profit_centers': ProfitCenter.objects.count(),
        'active_profit_centers': ProfitCenter.objects.filter(is_active=True).count(),
        'main_profit_centers': ProfitCenter.objects.filter(parent__isnull=True).count(),
        'sub_profit_centers': ProfitCenter.objects.filter(parent__isnull=False).count(),
        'printers': PrinterDefinition.objects.count(),
        'active_printers': PrinterDefinition.objects.filter(is_active=True).count(),
        'default_printers': PrinterDefinition.objects.filter(default_for_receipts=True).count(),
    }

    # حساب النسب المئوية
    percentages = {}
    for key, value in stats.items():
        if key.startswith('active_') and value > 0:
            base_key = key.replace('active_', '')
            if base_key in stats and stats[base_key] > 0:
                percentages[key] = round((value / stats[base_key]) * 100, 1)

    context = {
        'stats': stats,
        'percentages': percentages,
        'total_definitions': sum([
            stats['warehouses'], stats['products'], stats['categories'],
            stats['currencies'], stats['banks'], stats['cash_boxes'],
            stats['persons'], stats['units'], stats['asset_groups'],
            stats['asset_brands'], stats['expense_types'], stats['expense_names'],
            stats['revenue_types'], stats['revenue_names'], stats['profit_centers'],
            stats['printers']
        ]),
        'total_active': sum([
            stats['active_warehouses'], stats['active_products'], stats['active_categories'],
            stats['active_currencies'], stats['active_banks'], stats['active_cash_boxes'],
            stats['active_persons'], stats['active_units'], stats['active_asset_groups'],
            stats['active_asset_brands'], stats['active_expense_types'], stats['active_expense_names'],
            stats['active_revenue_types'], stats['active_revenue_names'], stats['active_profit_centers'],
            stats['active_printers']
        ])
    }

    return render(request, 'definitions/comprehensive_report.html', context)

# ===== أماكن الأصناف في المخازن =====

@login_required
def warehouse_location_list(request):
    """قائمة أماكن الأصناف في المخازن"""
    from .forms import WarehouseLocationForm

    # الحصول على جميع الأماكن
    locations = WarehouseLocation.objects.select_related('warehouse', 'created_by').all()

    # تطبيق الفلاتر
    warehouse_filter = request.GET.get('warehouse')
    status_filter = request.GET.get('status')
    search_query = request.GET.get('search')

    if warehouse_filter:
        locations = locations.filter(warehouse_id=warehouse_filter)

    if status_filter == 'active':
        locations = locations.filter(is_active=True)
    elif status_filter == 'inactive':
        locations = locations.filter(is_active=False)

    if search_query:
        locations = locations.filter(
            Q(name__icontains=search_query) |
            Q(code__icontains=search_query) |
            Q(description__icontains=search_query)
        )

    # ترتيب النتائج
    locations = locations.order_by('warehouse__name', 'code')

    # التصفح بالصفحات
    paginator = Paginator(locations, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # إحصائيات
    total_locations = WarehouseLocation.objects.count()
    active_locations = WarehouseLocation.objects.filter(is_active=True).count()
    full_locations = WarehouseLocation.objects.filter(
        max_capacity__isnull=False,
        current_capacity__gte=models.F('max_capacity')
    ).count()
    total_warehouses = WarehouseDefinition.objects.filter(is_active=True).count()

    # قائمة المخازن للفلتر
    warehouses = WarehouseDefinition.objects.filter(is_active=True).order_by('name')

    context = {
        'locations': page_obj,
        'warehouses': warehouses,
        'total_locations': total_locations,
        'active_locations': active_locations,
        'full_locations': full_locations,
        'total_warehouses': total_warehouses,
        'is_paginated': page_obj.has_other_pages(),
        'page_obj': page_obj,
    }

    return render(request, 'definitions/warehouse_location_list.html', context)

@login_required
def warehouse_location_create(request):
    """إضافة مكان جديد"""
    from .forms import WarehouseLocationForm

    import sys
    sys.stdout.write(f"DEBUG: warehouse_location_create called - Method: {request.method}\n")
    sys.stdout.flush()

    if request.method == 'POST':
        sys.stdout.write(f"DEBUG: POST request received\n")
        sys.stdout.write(f"DEBUG: POST data: {dict(request.POST)}\n")
        sys.stdout.flush()

        form = WarehouseLocationForm(request.POST)
        sys.stdout.write(f"DEBUG: Form created\n")
        sys.stdout.flush()

        if form.is_valid():
            sys.stdout.write(f"DEBUG: Form is valid\n")
            sys.stdout.flush()
            try:
                location = form.save(commit=False)
                location.created_by = request.user
                location.save()
                sys.stdout.write(f"DEBUG: Location saved successfully: {location.name}\n")
                sys.stdout.flush()
                messages.success(request, f'تم إضافة المكان "{location.name}" بنجاح')
                return redirect('definitions:warehouse_location_list')
            except Exception as e:
                sys.stdout.write(f"DEBUG: Error saving location: {e}\n")
                sys.stdout.flush()
                import traceback
                traceback.print_exc()
                messages.error(request, f'حدث خطأ أثناء حفظ المكان: {e}')
        else:
            sys.stdout.write(f"DEBUG: Form is not valid\n")
            sys.stdout.write(f"DEBUG: Form errors: {form.errors}\n")
            sys.stdout.write(f"DEBUG: Form non_field_errors: {form.non_field_errors()}\n")
            sys.stdout.flush()
            for field, errors in form.errors.items():
                sys.stdout.write(f"DEBUG: Field '{field}' errors: {errors}\n")
                sys.stdout.flush()

            # إضافة رسائل خطأ للمستخدم
            for field, errors in form.errors.items():
                for error in errors:
                    from dashboard.context_processors import user_settings
                    user_context = user_settings(request)
                    user_language = user_context.get('user_language', 'ar')

                    if user_language == 'en':
                        messages.error(request, f'Error in field {field}: {error}')
                    else:
                        messages.error(request, f'خطأ في حقل {field}: {error}')

            if form.non_field_errors():
                for error in form.non_field_errors():
                    if user_language == 'en':
                        messages.error(request, f'General error: {error}')
                    else:
                        messages.error(request, f'خطأ عام: {error}')
    else:
        sys.stdout.write(f"DEBUG: GET request - creating new form\n")
        sys.stdout.flush()
        form = WarehouseLocationForm()

    context = {
        'form': form,
        'title': 'إضافة مكان جديد'
    }

    return render(request, 'definitions/warehouse_location_form.html', context)

@login_required
def warehouse_location_detail(request, location_id):
    """عرض تفاصيل المكان"""
    location = get_object_or_404(WarehouseLocation, id=location_id)

    context = {
        'object': location,
    }

    return render(request, 'definitions/warehouse_location_detail.html', context)

@login_required
def warehouse_location_edit(request, location_id):
    """تعديل المكان"""
    from .forms import WarehouseLocationForm

    location = get_object_or_404(WarehouseLocation, id=location_id)

    if request.method == 'POST':
        form = WarehouseLocationForm(request.POST, instance=location)
        if form.is_valid():
            form.save()
            messages.success(request, f'تم تحديث المكان "{location.name}" بنجاح')
            return redirect('definitions:warehouse_location_detail', location.id)
    else:
        form = WarehouseLocationForm(instance=location)

    context = {
        'form': form,
        'object': location,
        'title': f'تعديل المكان: {location.name}'
    }

    return render(request, 'definitions/warehouse_location_form.html', context)

@login_required
def warehouse_location_delete(request, location_id):
    """حذف المكان مباشر مع تأكيد JavaScript فقط"""
    if request.method == 'POST':
        try:
            location = get_object_or_404(WarehouseLocation, id=location_id)
            location_name = location.name

            # حذف المكان مباشرة
            location.delete()

            # رسالة نجاح
            messages.success(request, f'تم حذف المكان "{location_name}" بنجاح!')

        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء حذف المكان: {e}')

    # العودة لقائمة الأماكن في جميع الحالات
    return redirect('definitions:warehouse_location_list')

# ===== إدارة الأصناف =====



@login_required
def product_create(request):
    """إضافة صنف جديد"""
    from .forms import ProductDefinitionForm

    if request.method == 'POST':
        print("تم استلام POST request")  # للتشخيص
        form = ProductDefinitionForm(request.POST, request.FILES)
        print(f"بيانات النموذج: {request.POST}")  # للتشخيص

        if form.is_valid():
            print("النموذج صحيح")  # للتشخيص
            try:
                product = form.save(commit=False)
                product.created_by = request.user

                # إصلاح مشكلة الباركود الفارغ
                if not product.barcode:
                    product.barcode = None

                product.save()
                print(f"تم حفظ الصنف: {product.name}")  # للتشخيص
                messages.success(request, f'تم إضافة الصنف "{product.name}" بنجاح')
                return redirect('definitions:product_detail', product.id)
            except Exception as e:
                print(f"خطأ في الحفظ: {e}")  # للتشخيص
                messages.error(request, f'حدث خطأ أثناء حفظ الصنف: {e}')
        else:
            print(f"أخطاء النموذج: {form.errors}")  # للتشخيص
            messages.error(request, 'يرجى تصحيح الأخطاء في النموذج')
    else:
        form = ProductDefinitionForm()

    context = {
        'form': form,
        'title': 'إضافة صنف جديد'
    }

    return render(request, 'definitions/product_form.html', context)

@login_required
def product_detail(request, product_id):
    """عرض تفاصيل الصنف"""
    product = get_object_or_404(ProductDefinition, id=product_id)

    context = {
        'object': product,
    }

    return render(request, 'definitions/product_detail.html', context)

@login_required
def product_edit(request, product_id):
    """تعديل الصنف"""
    from .forms import ProductDefinitionForm

    product = get_object_or_404(ProductDefinition, id=product_id)

    if request.method == 'POST':
        form = ProductDefinitionForm(request.POST, request.FILES, instance=product)
        if form.is_valid():
            form.save()
            messages.success(request, f'تم تحديث الصنف "{product.name}" بنجاح')
            return redirect('definitions:product_detail', product.id)
    else:
        form = ProductDefinitionForm(instance=product)

    context = {
        'form': form,
        'object': product,
        'title': f'تعديل الصنف: {product.name}'
    }

    return render(request, 'definitions/product_form.html', context)

@login_required
def product_delete(request, product_id):
    """حذف الصنف"""
    try:
        product = get_object_or_404(ProductDefinition, id=product_id)
        print(f"DEBUG: Found product: {product.name}")  # للتشخيص

        if request.method == 'POST':
            print(f"DEBUG: POST request received for product: {product.name}")  # للتشخيص
            try:
                product_name = product.name
                product.delete()
                print(f"DEBUG: Product deleted successfully: {product_name}")  # للتشخيص
                messages.success(request, f'تم حذف الصنف "{product_name}" بنجاح')
                return redirect('definitions:product_list')
            except Exception as e:
                print(f"DEBUG: Error deleting product: {e}")  # للتشخيص
                messages.error(request, f'حدث خطأ أثناء حذف الصنف: {e}')
                return redirect('definitions:product_list')

        print(f"DEBUG: Rendering delete confirmation page")  # للتشخيص
        context = {
            'object': product,
            'title': f'حذف الصنف: {product.name}'
        }

        return render(request, 'definitions/product_confirm_delete.html', context)

    except Exception as e:
        print(f"DEBUG: Error in product_delete view: {e}")  # للتشخيص
        import traceback
        traceback.print_exc()
        from django.http import HttpResponse
        return HttpResponse(f"خطأ في عرض صفحة الحذف: {e}", status=500)

@login_required
def product_duplicate(request, product_id):
    """نسخ الصنف"""
    from .forms import ProductDefinitionForm

    original_product = get_object_or_404(ProductDefinition, id=product_id)

    if request.method == 'POST':
        form = ProductDefinitionForm(request.POST, request.FILES)
        if form.is_valid():
            product = form.save(commit=False)
            product.created_by = request.user
            product.save()
            messages.success(request, f'تم نسخ الصنف "{product.name}" بنجاح')
            return redirect('definitions:product_detail', product.id)
    else:
        # إنشاء نسخة من الصنف الأصلي
        form_data = {
            'code': f"{original_product.code}_copy",
            'name': f"{original_product.name} (نسخة)",
            'description': original_product.description,
            'category': original_product.category,
            'cost_price': original_product.cost_price,
            'selling_price': original_product.selling_price,
            'wholesale_price': original_product.wholesale_price,
            'unit': original_product.unit,
            'current_stock': 0,  # البدء بمخزون صفر للنسخة
            'minimum_stock': original_product.minimum_stock,
            'maximum_stock': original_product.maximum_stock,
            'is_active': original_product.is_active,
            'track_stock': original_product.track_stock,
        }
        form = ProductDefinitionForm(initial=form_data)

    context = {
        'form': form,
        'original_product': original_product,
        'title': f'نسخ الصنف: {original_product.name}'
    }

    return render(request, 'definitions/product_form.html', context)

@login_required
def warehouse_list(request):
    """قائمة تعريف المخازن"""
    search_query = request.GET.get('search', '')
    warehouse_type = request.GET.get('type', '')

    warehouses = WarehouseDefinition.objects.all()

    if search_query:
        warehouses = warehouses.filter(
            Q(name__icontains=search_query) |
            Q(code__icontains=search_query) |
            Q(manager_name__icontains=search_query) |
            Q(address__icontains=search_query)
        )

    if warehouse_type:
        warehouses = warehouses.filter(warehouse_type=warehouse_type)

    warehouses = warehouses.order_by('-created_at')

    # Pagination
    paginator = Paginator(warehouses, 20)  # 20 مخزن في الصفحة للجدول
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # إحصائيات
    total_warehouses = WarehouseDefinition.objects.count()

    # أنواع المخازن للفلترة
    warehouse_types = WarehouseDefinition.WAREHOUSE_TYPES

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'warehouse_type': warehouse_type,
        'warehouse_types': warehouse_types,
        'total_warehouses': total_warehouses,
    }

    return render(request, 'definitions/warehouse_list.html', context)

@login_required
def warehouse_create(request):
    """إنشاء مخزن جديد"""
    if request.method == 'POST':
        # استخراج البيانات من النموذج
        code = request.POST.get('code')
        name = request.POST.get('name')
        warehouse_type = request.POST.get('warehouse_type')
        address = request.POST.get('address', '')
        phone = request.POST.get('phone', '')
        manager_name = request.POST.get('manager_name', '')
        description = request.POST.get('description', '')
        is_active = request.POST.get('is_active') == 'on'
        allow_negative_stock = request.POST.get('allow_negative_stock') == 'on'
        auto_reorder = request.POST.get('auto_reorder') == 'on'

        # التحقق من عدم تكرار الكود
        if WarehouseDefinition.objects.filter(code=code).exists():
            messages.error(request, f'كود المخزن "{code}" موجود بالفعل. يرجى استخدام كود آخر.')
            return render(request, 'definitions/warehouse_form.html', {
                'warehouse_types': WarehouseDefinition.WAREHOUSE_TYPES,
                'form_data': request.POST
            })

        # إنشاء المخزن
        warehouse = WarehouseDefinition.objects.create(
            code=code,
            name=name,
            warehouse_type=warehouse_type,
            address=address,
            phone=phone,
            manager_name=manager_name,
            description=description,
            is_active=is_active,
            allow_negative_stock=allow_negative_stock,
            auto_reorder=auto_reorder,
            created_by=request.user
        )

        messages.success(request, f'تم إنشاء المخزن "{warehouse.name}" بنجاح!')
        return redirect('definitions:warehouse_list')

    context = {
        'warehouse_types': WarehouseDefinition.WAREHOUSE_TYPES,
        'action': 'create'
    }

    return render(request, 'definitions/warehouse_form.html', context)

@login_required
def warehouse_edit(request, warehouse_id):
    """تعديل مخزن موجود"""
    warehouse = get_object_or_404(WarehouseDefinition, id=warehouse_id)

    if request.method == 'POST':
        # استخراج البيانات من النموذج
        code = request.POST.get('code')
        name = request.POST.get('name')
        warehouse_type = request.POST.get('warehouse_type')
        address = request.POST.get('address', '')
        phone = request.POST.get('phone', '')
        manager_name = request.POST.get('manager_name', '')
        description = request.POST.get('description', '')
        is_active = request.POST.get('is_active') == 'on'
        allow_negative_stock = request.POST.get('allow_negative_stock') == 'on'
        auto_reorder = request.POST.get('auto_reorder') == 'on'

        # التحقق من عدم تكرار الكود (باستثناء المخزن الحالي)
        if WarehouseDefinition.objects.filter(code=code).exclude(id=warehouse.id).exists():
            messages.error(request, f'كود المخزن "{code}" موجود بالفعل. يرجى استخدام كود آخر.')
            return render(request, 'definitions/warehouse_form.html', {
                'warehouse': warehouse,
                'warehouse_types': WarehouseDefinition.WAREHOUSE_TYPES,
                'action': 'edit',
                'form_data': request.POST
            })

        # تحديث المخزن
        warehouse.code = code
        warehouse.name = name
        warehouse.warehouse_type = warehouse_type
        warehouse.address = address
        warehouse.phone = phone
        warehouse.manager_name = manager_name
        warehouse.description = description
        warehouse.is_active = is_active
        warehouse.allow_negative_stock = allow_negative_stock
        warehouse.auto_reorder = auto_reorder
        warehouse.save()

        messages.success(request, f'تم تحديث المخزن "{warehouse.name}" بنجاح!')
        return redirect('definitions:warehouse_list')

    context = {
        'warehouse': warehouse,
        'warehouse_types': WarehouseDefinition.WAREHOUSE_TYPES,
        'action': 'edit'
    }

    return render(request, 'definitions/warehouse_form.html', context)

@login_required
def warehouse_detail(request, warehouse_id):
    """تفاصيل المخزن"""
    warehouse = get_object_or_404(WarehouseDefinition, id=warehouse_id)

    context = {
        'warehouse': warehouse,
    }

    return render(request, 'definitions/warehouse_detail.html', context)

@login_required
def warehouse_delete(request, warehouse_id):
    """حذف مخزن مباشر مع تأكيد JavaScript فقط"""
    print(f"DEBUG: warehouse_delete called with ID: {warehouse_id}")
    print(f"DEBUG: Request method: {request.method}")
    print(f"DEBUG: User: {request.user}")

    if request.method == 'POST':
        print(f"DEBUG: POST request received")
        try:
            warehouse = get_object_or_404(WarehouseDefinition, id=warehouse_id)
            warehouse_name = warehouse.name
            print(f"DEBUG: Found warehouse: {warehouse_name}")

            # حذف المخزن مباشرة
            warehouse.delete()
            print(f"DEBUG: Warehouse deleted successfully")

            # رسالة نجاح
            messages.success(request, f'تم حذف المخزن "{warehouse_name}" بنجاح!')
            print(f"DEBUG: Success message added")

        except Exception as e:
            print(f"DEBUG: Error occurred: {e}")
            import traceback
            traceback.print_exc()
            messages.error(request, f'حدث خطأ أثناء حذف المخزن: {e}')
    else:
        print(f"DEBUG: Non-POST request received")

    # العودة لقائمة المخازن في جميع الحالات
    print(f"DEBUG: Redirecting to warehouse_list")
    return redirect('definitions:warehouse_list')

@login_required
def warehouse_quick_delete(request, warehouse_id):
    """حذف سريع للمخزن من الجدول مباشرة"""
    if request.method == 'POST':
        try:
            warehouse = get_object_or_404(WarehouseDefinition, id=warehouse_id)
            print(f"DEBUG: Quick delete for warehouse: {warehouse.name}")  # للتشخيص

            # تم تعطيل فحص البيانات المرتبطة مؤقتاً لأن نماذج warehouses معطلة
            warehouse_name = warehouse.name
            warehouse.delete()
            print(f"DEBUG: Quick delete successful: {warehouse_name}")  # للتشخيص
            messages.success(request, f'تم حذف المخزن "{warehouse_name}" بنجاح!')

        except Exception as e:
            print(f"DEBUG: Error in quick delete: {e}")  # للتشخيص
            messages.error(request, f'حدث خطأ أثناء حذف المخزن: {e}')

        return redirect('definitions:warehouse_list')

    # إذا لم يكن POST، إعادة توجيه للقائمة
    return redirect('definitions:warehouse_list')

# ===== تعريف العملات =====
@login_required
def currency_list(request):
    """قائمة تعريف العملات"""
    search_query = request.GET.get('search', '')

    currencies = CurrencyDefinition.objects.all()

    if search_query:
        currencies = currencies.filter(
            Q(name__icontains=search_query) |
            Q(code__icontains=search_query) |
            Q(symbol__icontains=search_query)
        )

    currencies = currencies.order_by('-is_base_currency', 'code')

    paginator = Paginator(currencies, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'total_currencies': CurrencyDefinition.objects.count(),
    }

    return render(request, 'definitions/currency_list.html', context)

@login_required
def currency_create(request):
    """إنشاء عملة جديدة"""
    print("="*50)
    print(f"🚀 currency_create called with method: {request.method}")

    if request.method == 'POST':
        print("📝 POST request received")
        print(f"📊 POST data: {dict(request.POST)}")

        try:
            code = request.POST.get('code')
            name = request.POST.get('name')
            name_en = request.POST.get('name_en', '')
            symbol = request.POST.get('symbol')
            exchange_rate = request.POST.get('exchange_rate', '1.00')
            is_base_currency = request.POST.get('is_base_currency') == 'on'
            is_active = request.POST.get('is_active') == 'on'
            notes = request.POST.get('notes', '')

            print(f"📋 Parsed data:")
            print(f"   - code: '{code}'")
            print(f"   - name: '{name}'")
            print(f"   - name_en: '{name_en}'")
            print(f"   - symbol: '{symbol}'")
            print(f"   - exchange_rate: '{exchange_rate}'")
            print(f"   - is_base_currency: {is_base_currency}")
            print(f"   - is_active: {is_active}")
            print(f"   - notes: '{notes}'")

            # التحقق من الحقول المطلوبة
            if not code:
                print("❌ Error: كود العملة مطلوب")
                messages.error(request, 'كود العملة مطلوب')
                return render(request, 'definitions/currency_form.html', {
                    'action': 'create',
                    'form_data': request.POST
                })

            if not name:
                print("❌ Error: اسم العملة مطلوب")
                messages.error(request, 'اسم العملة مطلوب')
                return render(request, 'definitions/currency_form.html', {
                    'action': 'create',
                    'form_data': request.POST
                })

            if not symbol:
                print("❌ Error: رمز العملة مطلوب")
                messages.error(request, 'رمز العملة مطلوب')
                return render(request, 'definitions/currency_form.html', {
                    'action': 'create',
                    'form_data': request.POST
                })

            # التحقق من عدم تكرار الكود
            if CurrencyDefinition.objects.filter(code=code).exists():
                print(f"❌ Error: كود العملة {code} موجود بالفعل")
                messages.error(request, f'كود العملة "{code}" موجود بالفعل.')
                return render(request, 'definitions/currency_form.html', {
                    'action': 'create',
                    'form_data': request.POST
                })

            # إذا كانت عملة أساسية، إلغاء العملة الأساسية السابقة
            if is_base_currency:
                print("🔄 تحديث العملات الأساسية السابقة")
                CurrencyDefinition.objects.filter(is_base_currency=True).update(is_base_currency=False)

            print("💾 إنشاء العملة الجديدة...")
            currency = CurrencyDefinition.objects.create(
                code=code,
                name=name,
                name_en=name_en,
                symbol=symbol,
                exchange_rate=float(exchange_rate),
                is_base_currency=is_base_currency,
                is_active=is_active,
                notes=notes,
                created_by=request.user
            )

            print(f"✅ تم إنشاء العملة بنجاح: {currency.name} (ID: {currency.id})")
            messages.success(request, f'تم إنشاء العملة "{currency.name}" بنجاح!')
            return redirect('definitions:currency_list')

        except Exception as e:
            print("="*50)
            print(f"❌ CRITICAL ERROR creating currency: {e}")
            print(f"❌ Error type: {type(e).__name__}")
            import traceback
            print("❌ Full traceback:")
            traceback.print_exc()
            print("="*50)

            from dashboard.context_processors import user_settings
            user_context = user_settings(request)
            user_language = user_context.get('user_language', 'ar')

            if user_language == 'en':
                error_message = f'An error occurred while creating currency: {e}'
            else:
                error_message = f'حدث خطأ أثناء إنشاء العملة: {e}'
            messages.error(request, error_message)
            return render(request, 'definitions/currency_form.html', {
                'action': 'create',
                'form_data': request.POST,
                'error_message': error_message,
            })

    print("📄 GET request - عرض النموذج")
    return render(request, 'definitions/currency_form.html', {'action': 'create'})

@login_required
def currency_edit(request, currency_id):
    """تعديل عملة موجودة"""
    currency = get_object_or_404(CurrencyDefinition, id=currency_id)

    if request.method == 'POST':
        code = request.POST.get('code')
        name = request.POST.get('name')
        name_en = request.POST.get('name_en', '')
        symbol = request.POST.get('symbol')
        exchange_rate = request.POST.get('exchange_rate', '1.00')
        is_base_currency = request.POST.get('is_base_currency') == 'on'
        is_active = request.POST.get('is_active') == 'on'

        # التحقق من عدم تكرار الكود
        if CurrencyDefinition.objects.filter(code=code).exclude(id=currency.id).exists():
            messages.error(request, f'كود العملة "{code}" موجود بالفعل.')
            return render(request, 'definitions/currency_form.html', {
                'currency': currency, 'action': 'edit', 'form_data': request.POST
            })

        # إذا كانت عملة أساسية، إلغاء العملة الأساسية السابقة
        if is_base_currency and not currency.is_base_currency:
            CurrencyDefinition.objects.filter(is_base_currency=True).update(is_base_currency=False)

        currency.code = code
        currency.name = name
        currency.name_en = name_en
        currency.symbol = symbol
        currency.exchange_rate = exchange_rate
        currency.is_base_currency = is_base_currency
        currency.is_active = is_active
        currency.save()

        messages.success(request, f'تم تحديث العملة "{currency.name}" بنجاح!')
        return redirect('definitions:currency_list')

    context = {'currency': currency, 'action': 'edit'}
    return render(request, 'definitions/currency_form.html', context)

@login_required
def currency_detail(request, currency_id):
    """تفاصيل العملة"""
    currency = get_object_or_404(CurrencyDefinition, id=currency_id)
    return render(request, 'definitions/currency_detail.html', {'currency': currency})

@login_required
def currency_delete(request, currency_id):
    """حذف عملة"""
    currency = get_object_or_404(CurrencyDefinition, id=currency_id)

    if request.method == 'POST':
        # منع حذف العملة الأساسية
        if currency.is_base_currency:
            messages.error(request, 'لا يمكن حذف العملة الأساسية!')
            return redirect('definitions:currency_detail', currency_id=currency.id)

        currency_name = currency.name
        currency.delete()
        messages.success(request, f'تم حذف العملة "{currency_name}" بنجاح!')
        return redirect('definitions:currency_list')

    return render(request, 'definitions/currency_confirm_delete.html', {'currency': currency})

@login_required
def currency_quick_delete(request, currency_id):
    """حذف سريع للعملة"""
    if request.method == 'POST':
        currency = get_object_or_404(CurrencyDefinition, id=currency_id)

        if currency.is_base_currency:
            messages.error(request, f'لا يمكن حذف العملة الأساسية "{currency.name}"!')
        else:
            currency_name = currency.name
            currency.delete()
            messages.success(request, f'تم حذف العملة "{currency_name}" بنجاح!')

        return redirect('definitions:currency_list')

    return redirect('definitions:currency_list')

# ===== تعريف البنوك =====
@login_required
def bank_list(request):
    """قائمة تعريف البنوك"""
    search_query = request.GET.get('search', '')

    banks = BankDefinition.objects.all()

    if search_query:
        banks = banks.filter(
            Q(name__icontains=search_query) |
            Q(code__icontains=search_query) |
            Q(swift_code__icontains=search_query)
        )

    banks = banks.order_by('code')

    paginator = Paginator(banks, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'total_banks': BankDefinition.objects.count(),
    }

    return render(request, 'definitions/bank_list.html', context)

@login_required
def bank_create(request):
    """إنشاء بنك جديد"""
    if request.method == 'POST':
        code = request.POST.get('code')
        name = request.POST.get('name')
        name_en = request.POST.get('name_en', '')
        swift_code = request.POST.get('swift_code', '')
        address = request.POST.get('address', '')
        phone = request.POST.get('phone', '')
        is_active = request.POST.get('is_active') == 'on'

        if BankDefinition.objects.filter(code=code).exists():
            messages.error(request, f'كود البنك "{code}" موجود بالفعل.')
            return render(request, 'definitions/bank_form.html', {'form_data': request.POST})

        bank = BankDefinition.objects.create(
            code=code,
            name=name,
            name_en=name_en,
            swift_code=swift_code,
            address=address,
            phone=phone,
            is_active=is_active,
            created_by=request.user
        )

        messages.success(request, f'تم إنشاء البنك "{bank.name}" بنجاح!')
        return redirect('definitions:bank_list')

    return render(request, 'definitions/bank_form.html', {'action': 'create'})

@login_required
def bank_edit(request, bank_id):
    """تعديل بنك موجود"""
    bank = get_object_or_404(BankDefinition, id=bank_id)

    if request.method == 'POST':
        code = request.POST.get('code')
        name = request.POST.get('name')
        name_en = request.POST.get('name_en', '')
        swift_code = request.POST.get('swift_code', '')
        address = request.POST.get('address', '')
        phone = request.POST.get('phone', '')
        is_active = request.POST.get('is_active') == 'on'

        if BankDefinition.objects.filter(code=code).exclude(id=bank.id).exists():
            messages.error(request, f'كود البنك "{code}" موجود بالفعل.')
            return render(request, 'definitions/bank_form.html', {
                'bank': bank, 'action': 'edit', 'form_data': request.POST
            })

        bank.code = code
        bank.name = name
        bank.name_en = name_en
        bank.swift_code = swift_code
        bank.address = address
        bank.phone = phone
        bank.is_active = is_active
        bank.save()

        messages.success(request, f'تم تحديث البنك "{bank.name}" بنجاح!')
        return redirect('definitions:bank_list')

    context = {'bank': bank, 'action': 'edit'}
    return render(request, 'definitions/bank_form.html', context)

@login_required
def bank_detail(request, bank_id):
    """تفاصيل البنك"""
    bank = get_object_or_404(BankDefinition, id=bank_id)
    return render(request, 'definitions/bank_detail.html', {'bank': bank})

@login_required
def bank_delete(request, bank_id):
    """حذف بنك"""
    bank = get_object_or_404(BankDefinition, id=bank_id)

    if request.method == 'POST':
        bank_name = bank.name
        bank.delete()
        messages.success(request, f'تم حذف البنك "{bank_name}" بنجاح!')
        return redirect('definitions:bank_list')

    return render(request, 'definitions/bank_confirm_delete.html', {'bank': bank})

@login_required
def bank_quick_delete(request, bank_id):
    """حذف سريع للبنك"""
    if request.method == 'POST':
        bank = get_object_or_404(BankDefinition, id=bank_id)
        bank_name = bank.name
        bank.delete()
        messages.success(request, f'تم حذف البنك "{bank_name}" بنجاح!')
        return redirect('definitions:bank_list')

    return redirect('definitions:bank_list')

# ===== تعريف وحدات القياس =====
@login_required
def unit_list(request):
    """قائمة وحدات القياس"""
    search_query = request.GET.get('search', '')
    unit_type = request.GET.get('unit_type', '')

    units = UnitDefinition.objects.all()

    if search_query:
        units = units.filter(
            Q(code__icontains=search_query) |
            Q(name__icontains=search_query) |
            Q(name_en__icontains=search_query)
        )

    if unit_type:
        units = units.filter(unit_type=unit_type)

    units = units.order_by('unit_type', 'name')

    # إحصائيات
    total_units = UnitDefinition.objects.count()
    active_units = UnitDefinition.objects.filter(is_active=True).count()
    base_units = UnitDefinition.objects.filter(is_base_unit=True).count()

    context = {
        'units': units,
        'search_query': search_query,
        'unit_type': unit_type,
        'unit_types': UnitDefinition.UNIT_TYPES,
        'total_units': total_units,
        'active_units': active_units,
        'base_units': base_units,
    }

    return render(request, 'definitions/unit_list.html', context)

@login_required
def unit_create(request):
    """إنشاء وحدة قياس جديدة"""
    print("="*50)
    print(f"🚀 unit_create called with method: {request.method}")

    if request.method == 'POST':
        print("📝 POST request received")
        print(f"📊 POST data: {dict(request.POST)}")

        try:
            code = request.POST.get('code')
            name = request.POST.get('name')
            name_en = request.POST.get('name_en', '')
            unit_type = request.POST.get('unit_type')
            decimal_places = request.POST.get('decimal_places', '2')
            base_unit_id = request.POST.get('base_unit')
            conversion_factor = request.POST.get('conversion_factor', '1.0000')
            is_base_unit = request.POST.get('is_base_unit') == 'on'
            description = request.POST.get('description', '')
            notes = request.POST.get('notes', '')
            is_active = request.POST.get('is_active') == 'on'

            print(f"📋 Parsed data:")
            print(f"   - code: '{code}'")
            print(f"   - name: '{name}'")
            print(f"   - unit_type: '{unit_type}'")
            print(f"   - is_base_unit: {is_base_unit}")

            # التحقق من الحقول المطلوبة
            if not code:
                print("❌ Error: كود وحدة القياس مطلوب")
                messages.error(request, 'كود وحدة القياس مطلوب')
                return render(request, 'definitions/unit_form.html', {
                    'action': 'create',
                    'form_data': request.POST,
                    'base_units': UnitDefinition.objects.filter(is_base_unit=True, is_active=True)
                })

            if not name:
                print("❌ Error: اسم وحدة القياس مطلوب")
                messages.error(request, 'اسم وحدة القياس مطلوب')
                return render(request, 'definitions/unit_form.html', {
                    'action': 'create',
                    'form_data': request.POST,
                    'base_units': UnitDefinition.objects.filter(is_base_unit=True, is_active=True)
                })

            if not unit_type:
                print("❌ Error: نوع وحدة القياس مطلوب")
                messages.error(request, 'نوع وحدة القياس مطلوب')
                return render(request, 'definitions/unit_form.html', {
                    'action': 'create',
                    'form_data': request.POST,
                    'base_units': UnitDefinition.objects.filter(is_base_unit=True, is_active=True)
                })

            # التحقق من عدم تكرار الكود
            if UnitDefinition.objects.filter(code=code).exists():
                print(f"❌ Error: كود وحدة القياس {code} موجود بالفعل")
                messages.error(request, f'كود وحدة القياس "{code}" موجود بالفعل.')
                return render(request, 'definitions/unit_form.html', {
                    'action': 'create',
                    'form_data': request.POST,
                    'base_units': UnitDefinition.objects.filter(is_base_unit=True, is_active=True)
                })

            # معالجة الوحدة الأساسية
            base_unit = None
            if not is_base_unit and base_unit_id:
                try:
                    base_unit = UnitDefinition.objects.get(id=base_unit_id)
                except UnitDefinition.DoesNotExist:
                    base_unit = None

            print("💾 إنشاء وحدة القياس الجديدة...")
            unit = UnitDefinition.objects.create(
                code=code,
                name=name,
                name_en=name_en,
                unit_type=unit_type,
                decimal_places=int(decimal_places),
                base_unit=base_unit,
                conversion_factor=float(conversion_factor),
                is_base_unit=is_base_unit,
                description=description,
                notes=notes,
                is_active=is_active,
                created_by=request.user
            )

            print(f"✅ تم إنشاء وحدة القياس بنجاح: {unit.name} (ID: {unit.id})")
            messages.success(request, f'تم إنشاء وحدة القياس "{unit.name}" بنجاح!')
            return redirect('definitions:unit_list')

        except Exception as e:
            print("="*50)
            print(f"❌ CRITICAL ERROR creating unit: {e}")
            print(f"❌ Error type: {type(e).__name__}")
            import traceback
            print("❌ Full traceback:")
            traceback.print_exc()
            print("="*50)

            error_message = f'حدث خطأ أثناء إنشاء وحدة القياس: {e}'
            messages.error(request, error_message)
            return render(request, 'definitions/unit_form.html', {
                'action': 'create',
                'form_data': request.POST,
                'base_units': UnitDefinition.objects.filter(is_base_unit=True, is_active=True),
                'error_message': error_message,
            })

    print("📄 GET request - عرض النموذج")
    return render(request, 'definitions/unit_form.html', {
        'action': 'create',
        'base_units': UnitDefinition.objects.filter(is_base_unit=True, is_active=True)
    })

@login_required
def unit_detail(request, unit_id):
    """تفاصيل وحدة القياس"""
    unit = get_object_or_404(UnitDefinition, id=unit_id)
    return render(request, 'definitions/unit_detail.html', {'unit': unit})

@login_required
def unit_edit(request, unit_id):
    """تعديل وحدة قياس موجودة"""
    unit = get_object_or_404(UnitDefinition, id=unit_id)

    if request.method == 'POST':
        try:
            code = request.POST.get('code')
            name = request.POST.get('name')
            name_en = request.POST.get('name_en', '')
            unit_type = request.POST.get('unit_type')
            decimal_places = request.POST.get('decimal_places', '2')
            is_base_unit = request.POST.get('is_base_unit') == 'on'
            description = request.POST.get('description', '')
            notes = request.POST.get('notes', '')
            is_active = request.POST.get('is_active') == 'on'

            # التحقق من عدم تكرار الكود (باستثناء الوحدة الحالية)
            if UnitDefinition.objects.filter(code=code).exclude(id=unit.id).exists():
                messages.error(request, f'كود وحدة القياس "{code}" موجود بالفعل.')
                return render(request, 'definitions/unit_form.html', {
                    'action': 'edit',
                    'unit': unit,
                    'form_data': request.POST,
                    'base_units': UnitDefinition.objects.filter(is_base_unit=True, is_active=True).exclude(id=unit.id)
                })

            # تحديث البيانات
            unit.code = code
            unit.name = name
            unit.name_en = name_en
            unit.unit_type = unit_type
            unit.decimal_places = int(decimal_places)
            unit.is_base_unit = is_base_unit
            unit.description = description
            unit.notes = notes
            unit.is_active = is_active
            unit.save()

            messages.success(request, f'تم تحديث وحدة القياس "{unit.name}" بنجاح!')
            return redirect('definitions:unit_detail', unit_id=unit.id)

        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء تحديث وحدة القياس: {e}')

    return render(request, 'definitions/unit_form.html', {
        'action': 'edit',
        'unit': unit,
        'base_units': UnitDefinition.objects.filter(is_base_unit=True, is_active=True).exclude(id=unit.id)
    })

@login_required
def unit_delete(request, unit_id):
    """حذف وحدة قياس"""
    unit = get_object_or_404(UnitDefinition, id=unit_id)

    if request.method == 'POST':
        unit_name = unit.name
        unit.delete()
        messages.success(request, f'تم حذف وحدة القياس "{unit_name}" بنجاح!')
        return redirect('definitions:unit_list')

    return render(request, 'definitions/unit_confirm_delete.html', {'unit': unit})

@login_required
def unit_quick_delete(request, unit_id):
    """حذف سريع لوحدة القياس"""
    if request.method == 'POST':
        unit = get_object_or_404(UnitDefinition, id=unit_id)
        unit_name = unit.name
        unit.delete()
        messages.success(request, f'تم حذف وحدة القياس "{unit_name}" بنجاح!')

    return redirect('definitions:unit_list')


# ===== مجموعات الأصول =====

@login_required
def asset_group_list(request):
    """قائمة مجموعات الأصول"""
    # الحصول على جميع مجموعات الأصول
    asset_groups = AssetGroup.objects.select_related('created_by').all().order_by('name')

    # إحصائيات
    total_groups = asset_groups.count()
    active_groups = asset_groups.filter(is_active=True).count()

    context = {
        'asset_groups': asset_groups,
        'total_groups': total_groups,
        'active_groups': active_groups,
    }

    return render(request, 'definitions/asset_group_list.html', context)

@login_required
def asset_group_create(request):
    """إضافة مجموعة أصول جديدة"""
    if request.method == 'POST':
        try:
            code = request.POST.get('code')
            name = request.POST.get('name')
            name_en = request.POST.get('name_en', '')
            description = request.POST.get('description', '')
            notes = request.POST.get('notes', '')
            is_active = request.POST.get('is_active') == 'on'

            # التحقق من عدم تكرار الكود
            if AssetGroup.objects.filter(code=code).exists():
                messages.error(request, f'كود مجموعة الأصول "{code}" موجود بالفعل.')
                return render(request, 'definitions/asset_group_form.html', {
                    'action': 'create',
                    'form_data': request.POST
                })

            # إنشاء مجموعة الأصول
            asset_group = AssetGroup.objects.create(
                code=code,
                name=name,
                name_en=name_en,
                description=description,
                notes=notes,
                is_active=is_active,
                created_by=request.user
            )

            messages.success(request, f'تم إضافة مجموعة الأصول "{asset_group.name}" بنجاح!')
            return redirect('definitions:asset_group_detail', group_id=asset_group.id)

        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء إضافة مجموعة الأصول: {e}')

    return render(request, 'definitions/asset_group_form.html', {'action': 'create'})

@login_required
def asset_group_quick_create(request):
    """إضافة سريعة لمجموعة أصول"""
    if request.method == 'POST':
        try:
            code = request.POST.get('code', '').strip().upper()
            name = request.POST.get('name', '').strip()

            if not code or not name:
                messages.error(request, 'يجب إدخال الكود والاسم.')
                return redirect('definitions:asset_group_list')

            # التحقق من عدم تكرار الكود
            if AssetGroup.objects.filter(code=code).exists():
                messages.error(request, f'كود مجموعة الأصول "{code}" موجود بالفعل.')
                return redirect('definitions:asset_group_list')

            # إنشاء مجموعة الأصول
            asset_group = AssetGroup.objects.create(
                code=code,
                name=name,
                is_active=True,
                created_by=request.user
            )

            messages.success(request, f'تم إضافة مجموعة الأصول "{asset_group.name}" بنجاح!')

        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء إضافة مجموعة الأصول: {e}')

    return redirect('definitions:asset_group_list')

@login_required
def asset_group_detail(request, group_id):
    """تفاصيل مجموعة الأصول"""
    asset_group = get_object_or_404(AssetGroup, id=group_id)
    return render(request, 'definitions/asset_group_detail.html', {'asset_group': asset_group})

@login_required
def asset_group_edit(request, group_id):
    """تعديل مجموعة أصول موجودة"""
    asset_group = get_object_or_404(AssetGroup, id=group_id)

    if request.method == 'POST':
        try:
            code = request.POST.get('code')
            name = request.POST.get('name')
            name_en = request.POST.get('name_en', '')
            description = request.POST.get('description', '')
            notes = request.POST.get('notes', '')
            is_active = request.POST.get('is_active') == 'on'

            # التحقق من عدم تكرار الكود (باستثناء المجموعة الحالية)
            if AssetGroup.objects.filter(code=code).exclude(id=asset_group.id).exists():
                messages.error(request, f'كود مجموعة الأصول "{code}" موجود بالفعل.')
                return render(request, 'definitions/asset_group_form.html', {
                    'action': 'edit',
                    'asset_group': asset_group,
                    'form_data': request.POST
                })

            # تحديث البيانات
            asset_group.code = code
            asset_group.name = name
            asset_group.name_en = name_en
            asset_group.description = description
            asset_group.notes = notes
            asset_group.is_active = is_active
            asset_group.save()

            messages.success(request, f'تم تحديث مجموعة الأصول "{asset_group.name}" بنجاح!')
            return redirect('definitions:asset_group_detail', group_id=asset_group.id)

        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء تحديث مجموعة الأصول: {e}')

    return render(request, 'definitions/asset_group_form.html', {
        'action': 'edit',
        'asset_group': asset_group
    })

@login_required
def asset_group_delete(request, group_id):
    """حذف مجموعة أصول"""
    asset_group = get_object_or_404(AssetGroup, id=group_id)

    if request.method == 'POST':
        group_name = asset_group.name
        asset_group.delete()
        messages.success(request, f'تم حذف مجموعة الأصول "{group_name}" بنجاح!')
        return redirect('definitions:asset_group_list')

    return render(request, 'definitions/asset_group_confirm_delete.html', {'asset_group': asset_group})

@login_required
def asset_group_quick_delete(request, group_id):
    """حذف سريع لمجموعة الأصول"""
    if request.method == 'POST':
        asset_group = get_object_or_404(AssetGroup, id=group_id)
        group_name = asset_group.name
        asset_group.delete()
        messages.success(request, f'تم حذف مجموعة الأصول "{group_name}" بنجاح!')

    return redirect('definitions:asset_group_list')


# ===== ماركات الأصول =====

@login_required
def asset_brand_list(request):
    """قائمة ماركات الأصول"""
    # الحصول على جميع ماركات الأصول
    asset_brands = AssetBrand.objects.select_related('created_by').all().order_by('name')

    # إحصائيات
    total_brands = asset_brands.count()
    active_brands = asset_brands.filter(is_active=True).count()
    countries_count = asset_brands.exclude(country_of_origin__isnull=True).exclude(country_of_origin__exact='').values('country_of_origin').distinct().count()

    context = {
        'asset_brands': asset_brands,
        'total_brands': total_brands,
        'active_brands': active_brands,
        'countries_count': countries_count,
    }

    return render(request, 'definitions/asset_brand_list.html', context)

@login_required
def asset_brand_create(request):
    """إضافة ماركة أصول جديدة"""
    if request.method == 'POST':
        try:
            code = request.POST.get('code')
            name = request.POST.get('name')
            name_en = request.POST.get('name_en', '')
            country_of_origin = request.POST.get('country_of_origin', '')
            website = request.POST.get('website', '')
            contact_info = request.POST.get('contact_info', '')
            description = request.POST.get('description', '')
            notes = request.POST.get('notes', '')
            is_active = request.POST.get('is_active') == 'on'

            # التحقق من عدم تكرار الكود
            if AssetBrand.objects.filter(code=code).exists():
                messages.error(request, f'كود ماركة الأصول "{code}" موجود بالفعل.')
                return render(request, 'definitions/asset_brand_form.html', {
                    'action': 'create',
                    'form_data': request.POST
                })

            # إنشاء ماركة الأصول
            asset_brand = AssetBrand.objects.create(
                code=code,
                name=name,
                name_en=name_en,
                country_of_origin=country_of_origin,
                website=website,
                contact_info=contact_info,
                description=description,
                notes=notes,
                is_active=is_active,
                created_by=request.user
            )

            messages.success(request, f'تم إضافة ماركة الأصول "{asset_brand.name}" بنجاح!')
            return redirect('definitions:asset_brand_detail', brand_id=asset_brand.id)

        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء إضافة ماركة الأصول: {e}')

    return render(request, 'definitions/asset_brand_form.html', {'action': 'create'})

@login_required
def asset_brand_detail(request, brand_id):
    """تفاصيل ماركة الأصول"""
    asset_brand = get_object_or_404(AssetBrand, id=brand_id)
    return render(request, 'definitions/asset_brand_detail.html', {'asset_brand': asset_brand})

@login_required
def asset_brand_edit(request, brand_id):
    """تعديل ماركة أصول موجودة"""
    asset_brand = get_object_or_404(AssetBrand, id=brand_id)

    if request.method == 'POST':
        try:
            code = request.POST.get('code')
            name = request.POST.get('name')
            name_en = request.POST.get('name_en', '')
            country_of_origin = request.POST.get('country_of_origin', '')
            website = request.POST.get('website', '')
            contact_info = request.POST.get('contact_info', '')
            description = request.POST.get('description', '')
            notes = request.POST.get('notes', '')
            is_active = request.POST.get('is_active') == 'on'

            # التحقق من عدم تكرار الكود (باستثناء الماركة الحالية)
            if AssetBrand.objects.filter(code=code).exclude(id=asset_brand.id).exists():
                messages.error(request, f'كود ماركة الأصول "{code}" موجود بالفعل.')
                return render(request, 'definitions/asset_brand_form.html', {
                    'action': 'edit',
                    'asset_brand': asset_brand,
                    'form_data': request.POST
                })

            # تحديث البيانات
            asset_brand.code = code
            asset_brand.name = name
            asset_brand.name_en = name_en
            asset_brand.country_of_origin = country_of_origin
            asset_brand.website = website
            asset_brand.contact_info = contact_info
            asset_brand.description = description
            asset_brand.notes = notes
            asset_brand.is_active = is_active
            asset_brand.save()

            messages.success(request, f'تم تحديث ماركة الأصول "{asset_brand.name}" بنجاح!')
            return redirect('definitions:asset_brand_detail', brand_id=asset_brand.id)

        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء تحديث ماركة الأصول: {e}')

    return render(request, 'definitions/asset_brand_form.html', {
        'action': 'edit',
        'asset_brand': asset_brand
    })

@login_required
def asset_brand_delete(request, brand_id):
    """حذف ماركة أصول"""
    asset_brand = get_object_or_404(AssetBrand, id=brand_id)

    if request.method == 'POST':
        brand_name = asset_brand.name
        asset_brand.delete()
        messages.success(request, f'تم حذف ماركة الأصول "{brand_name}" بنجاح!')
        return redirect('definitions:asset_brand_list')

    return render(request, 'definitions/asset_brand_confirm_delete.html', {'asset_brand': asset_brand})

@login_required
def asset_brand_quick_delete(request, brand_id):
    """حذف سريع لماركة الأصول"""
    if request.method == 'POST':
        asset_brand = get_object_or_404(AssetBrand, id=brand_id)
        brand_name = asset_brand.name
        asset_brand.delete()
        messages.success(request, f'تم حذف ماركة الأصول "{brand_name}" بنجاح!')

    return redirect('definitions:asset_brand_list')


# ===== أنواع المصروفات =====

@login_required
def expense_type_list(request):
    """قائمة أنواع المصروفات"""
    # الحصول على جميع أنواع المصروفات
    expense_types = ExpenseType.objects.select_related('created_by').all().order_by('name')

    # إحصائيات
    total_types = expense_types.count()
    active_types = expense_types.filter(is_active=True).count()
    fixed_types = expense_types.filter(expense_type='fixed').count()
    variable_types = expense_types.filter(expense_type='variable').count()

    context = {
        'expense_types': expense_types,
        'total_types': total_types,
        'active_types': active_types,
        'fixed_types': fixed_types,
        'variable_types': variable_types,
    }

    return render(request, 'definitions/expense_type_list.html', context)

@login_required
def expense_type_create(request):
    """إضافة نوع مصروف جديد"""
    if request.method == 'POST':
        try:
            code = request.POST.get('code')
            name = request.POST.get('name')
            name_en = request.POST.get('name_en', '')
            expense_type = request.POST.get('expense_type', 'fixed')
            category = request.POST.get('category', 'operational')
            description = request.POST.get('description', '')
            notes = request.POST.get('notes', '')
            is_active = request.POST.get('is_active') == 'on'

            # التحقق من عدم تكرار الكود
            if ExpenseType.objects.filter(code=code).exists():
                messages.error(request, f'كود نوع المصروف "{code}" موجود بالفعل.')
                return render(request, 'definitions/expense_type_form.html', {
                    'action': 'create',
                    'form_data': request.POST
                })

            # إنشاء نوع المصروف
            expense_type_obj = ExpenseType.objects.create(
                code=code,
                name=name,
                name_en=name_en,
                expense_type=expense_type,
                category=category,
                description=description,
                notes=notes,
                is_active=is_active,
                created_by=request.user
            )

            messages.success(request, f'تم إضافة نوع المصروف "{expense_type_obj.name}" بنجاح!')
            return redirect('definitions:expense_type_detail', type_id=expense_type_obj.id)

        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء إضافة نوع المصروف: {e}')

    return render(request, 'definitions/expense_type_form.html', {'action': 'create'})

@login_required
def expense_type_detail(request, type_id):
    """تفاصيل نوع المصروف"""
    expense_type = get_object_or_404(ExpenseType, id=type_id)
    return render(request, 'definitions/expense_type_detail.html', {'expense_type': expense_type})

@login_required
def expense_type_edit(request, type_id):
    """تعديل نوع مصروف موجود"""
    expense_type = get_object_or_404(ExpenseType, id=type_id)

    if request.method == 'POST':
        try:
            code = request.POST.get('code')
            name = request.POST.get('name')
            name_en = request.POST.get('name_en', '')
            expense_type_value = request.POST.get('expense_type', 'fixed')
            category = request.POST.get('category', 'operational')
            description = request.POST.get('description', '')
            notes = request.POST.get('notes', '')
            is_active = request.POST.get('is_active') == 'on'

            # التحقق من عدم تكرار الكود (باستثناء النوع الحالي)
            if ExpenseType.objects.filter(code=code).exclude(id=expense_type.id).exists():
                messages.error(request, f'كود نوع المصروف "{code}" موجود بالفعل.')
                return render(request, 'definitions/expense_type_form.html', {
                    'action': 'edit',
                    'expense_type': expense_type,
                    'form_data': request.POST
                })

            # تحديث البيانات
            expense_type.code = code
            expense_type.name = name
            expense_type.name_en = name_en
            expense_type.expense_type = expense_type_value
            expense_type.category = category
            expense_type.description = description
            expense_type.notes = notes
            expense_type.is_active = is_active
            expense_type.save()

            messages.success(request, f'تم تحديث نوع المصروف "{expense_type.name}" بنجاح!')
            return redirect('definitions:expense_type_detail', type_id=expense_type.id)

        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء تحديث نوع المصروف: {e}')

    return render(request, 'definitions/expense_type_form.html', {
        'action': 'edit',
        'expense_type': expense_type
    })

@login_required
def expense_type_delete(request, type_id):
    """حذف نوع مصروف"""
    expense_type = get_object_or_404(ExpenseType, id=type_id)

    if request.method == 'POST':
        type_name = expense_type.name
        expense_type.delete()
        messages.success(request, f'تم حذف نوع المصروف "{type_name}" بنجاح!')
        return redirect('definitions:expense_type_list')

    return render(request, 'definitions/expense_type_confirm_delete.html', {'expense_type': expense_type})

@login_required
def expense_type_quick_delete(request, type_id):
    """حذف سريع لنوع المصروف"""
    if request.method == 'POST':
        expense_type = get_object_or_404(ExpenseType, id=type_id)
        type_name = expense_type.name
        expense_type.delete()
        messages.success(request, f'تم حذف نوع المصروف "{type_name}" بنجاح!')

    return redirect('definitions:expense_type_list')


# ===== أسماء المصروفات =====

@login_required
def expense_name_list(request):
    """قائمة أسماء المصروفات"""
    # الحصول على جميع أسماء المصروفات
    expense_names = ExpenseName.objects.select_related('expense_type', 'created_by').all().order_by('name')

    # إحصائيات
    total_names = expense_names.count()
    active_names = expense_names.filter(is_active=True).count()
    with_type_count = expense_names.exclude(expense_type__isnull=True).count()

    context = {
        'expense_names': expense_names,
        'total_names': total_names,
        'active_names': active_names,
        'with_type_count': with_type_count,
    }

    return render(request, 'definitions/expense_name_list.html', context)

@login_required
def expense_name_create(request):
    """إضافة اسم مصروف جديد"""
    if request.method == 'POST':
        try:
            code = request.POST.get('code')
            name = request.POST.get('name')
            name_en = request.POST.get('name_en', '')
            expense_type_id = request.POST.get('expense_type')
            description = request.POST.get('description', '')
            notes = request.POST.get('notes', '')
            is_active = request.POST.get('is_active') == 'on'

            # التحقق من عدم تكرار الكود
            if ExpenseName.objects.filter(code=code).exists():
                messages.error(request, f'كود اسم المصروف "{code}" موجود بالفعل.')
                return render(request, 'definitions/expense_name_form.html', {
                    'action': 'create',
                    'form_data': request.POST,
                    'expense_types': ExpenseType.objects.filter(is_active=True).order_by('name')
                })

            # إنشاء اسم المصروف
            expense_name_obj = ExpenseName.objects.create(
                code=code,
                name=name,
                name_en=name_en,
                expense_type_id=expense_type_id if expense_type_id else None,
                description=description,
                notes=notes,
                is_active=is_active,
                created_by=request.user
            )

            messages.success(request, f'تم إضافة اسم المصروف "{expense_name_obj.name}" بنجاح!')
            return redirect('definitions:expense_name_detail', name_id=expense_name_obj.id)

        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء إضافة اسم المصروف: {e}')

    context = {
        'action': 'create',
        'expense_types': ExpenseType.objects.filter(is_active=True).order_by('name')
    }
    return render(request, 'definitions/expense_name_form.html', context)

@login_required
def expense_name_detail(request, name_id):
    """تفاصيل اسم المصروف"""
    expense_name = get_object_or_404(ExpenseName, id=name_id)
    return render(request, 'definitions/expense_name_detail.html', {'expense_name': expense_name})

@login_required
def expense_name_edit(request, name_id):
    """تعديل اسم مصروف موجود"""
    expense_name = get_object_or_404(ExpenseName, id=name_id)

    if request.method == 'POST':
        try:
            code = request.POST.get('code')
            name = request.POST.get('name')
            name_en = request.POST.get('name_en', '')
            expense_type_id = request.POST.get('expense_type')
            description = request.POST.get('description', '')
            notes = request.POST.get('notes', '')
            is_active = request.POST.get('is_active') == 'on'

            # التحقق من عدم تكرار الكود (باستثناء الاسم الحالي)
            if ExpenseName.objects.filter(code=code).exclude(id=expense_name.id).exists():
                messages.error(request, f'كود اسم المصروف "{code}" موجود بالفعل.')
                return render(request, 'definitions/expense_name_form.html', {
                    'action': 'edit',
                    'expense_name': expense_name,
                    'form_data': request.POST,
                    'expense_types': ExpenseType.objects.filter(is_active=True).order_by('name')
                })

            # تحديث البيانات
            expense_name.code = code
            expense_name.name = name
            expense_name.name_en = name_en
            expense_name.expense_type_id = expense_type_id if expense_type_id else None
            expense_name.description = description
            expense_name.notes = notes
            expense_name.is_active = is_active
            expense_name.save()

            messages.success(request, f'تم تحديث اسم المصروف "{expense_name.name}" بنجاح!')
            return redirect('definitions:expense_name_detail', name_id=expense_name.id)

        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء تحديث اسم المصروف: {e}')

    context = {
        'action': 'edit',
        'expense_name': expense_name,
        'expense_types': ExpenseType.objects.filter(is_active=True).order_by('name')
    }
    return render(request, 'definitions/expense_name_form.html', context)

@login_required
def expense_name_delete(request, name_id):
    """حذف اسم مصروف"""
    expense_name = get_object_or_404(ExpenseName, id=name_id)

    if request.method == 'POST':
        name_value = expense_name.name
        expense_name.delete()
        messages.success(request, f'تم حذف اسم المصروف "{name_value}" بنجاح!')
        return redirect('definitions:expense_name_list')

    return render(request, 'definitions/expense_name_confirm_delete.html', {'expense_name': expense_name})

@login_required
def expense_name_quick_delete(request, name_id):
    """حذف سريع لاسم المصروف"""
    if request.method == 'POST':
        expense_name = get_object_or_404(ExpenseName, id=name_id)
        name_value = expense_name.name
        expense_name.delete()
        messages.success(request, f'تم حذف اسم المصروف "{name_value}" بنجاح!')

    return redirect('definitions:expense_name_list')


# ===== أنواع الإيرادات =====

@login_required
def revenue_type_list(request):
    """قائمة أنواع الإيرادات"""
    # الحصول على جميع أنواع الإيرادات
    revenue_types = RevenueType.objects.select_related('created_by').all().order_by('name')

    # إحصائيات
    total_types = revenue_types.count()
    active_types = revenue_types.filter(is_active=True).count()
    recurring_types = revenue_types.filter(revenue_type='recurring').count()
    one_time_types = revenue_types.filter(revenue_type='one-time').count()

    context = {
        'revenue_types': revenue_types,
        'total_types': total_types,
        'active_types': active_types,
        'recurring_types': recurring_types,
        'one_time_types': one_time_types,
    }

    return render(request, 'definitions/revenue_type_list.html', context)

@login_required
def revenue_type_create(request):
    """إضافة نوع إيراد جديد"""
    if request.method == 'POST':
        try:
            code = request.POST.get('code')
            name = request.POST.get('name')
            name_en = request.POST.get('name_en', '')
            revenue_type = request.POST.get('revenue_type', 'recurring')
            category = request.POST.get('category', 'operational')
            description = request.POST.get('description', '')
            notes = request.POST.get('notes', '')
            is_active = request.POST.get('is_active') == 'on'

            # التحقق من عدم تكرار الكود
            if RevenueType.objects.filter(code=code).exists():
                messages.error(request, f'كود نوع الإيراد "{code}" موجود بالفعل.')
                return render(request, 'definitions/revenue_type_form.html', {
                    'action': 'create',
                    'form_data': request.POST
                })

            # إنشاء نوع الإيراد
            revenue_type_obj = RevenueType.objects.create(
                code=code,
                name=name,
                name_en=name_en,
                revenue_type=revenue_type,
                category=category,
                description=description,
                notes=notes,
                is_active=is_active,
                created_by=request.user
            )

            messages.success(request, f'تم إضافة نوع الإيراد "{revenue_type_obj.name}" بنجاح!')
            return redirect('definitions:revenue_type_detail', type_id=revenue_type_obj.id)

        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء إضافة نوع الإيراد: {e}')

    return render(request, 'definitions/revenue_type_form.html', {'action': 'create'})

@login_required
def revenue_type_detail(request, type_id):
    """تفاصيل نوع الإيراد"""
    revenue_type = get_object_or_404(RevenueType, id=type_id)
    return render(request, 'definitions/revenue_type_detail.html', {'revenue_type': revenue_type})

@login_required
def revenue_type_edit(request, type_id):
    """تعديل نوع إيراد موجود"""
    revenue_type = get_object_or_404(RevenueType, id=type_id)

    if request.method == 'POST':
        try:
            code = request.POST.get('code')
            name = request.POST.get('name')
            name_en = request.POST.get('name_en', '')
            revenue_type_value = request.POST.get('revenue_type', 'recurring')
            category = request.POST.get('category', 'operational')
            description = request.POST.get('description', '')
            notes = request.POST.get('notes', '')
            is_active = request.POST.get('is_active') == 'on'

            # التحقق من عدم تكرار الكود (باستثناء النوع الحالي)
            if RevenueType.objects.filter(code=code).exclude(id=revenue_type.id).exists():
                messages.error(request, f'كود نوع الإيراد "{code}" موجود بالفعل.')
                return render(request, 'definitions/revenue_type_form.html', {
                    'action': 'edit',
                    'revenue_type': revenue_type,
                    'form_data': request.POST
                })

            # تحديث البيانات
            revenue_type.code = code
            revenue_type.name = name
            revenue_type.name_en = name_en
            revenue_type.revenue_type = revenue_type_value
            revenue_type.category = category
            revenue_type.description = description
            revenue_type.notes = notes
            revenue_type.is_active = is_active
            revenue_type.save()

            messages.success(request, f'تم تحديث نوع الإيراد "{revenue_type.name}" بنجاح!')
            return redirect('definitions:revenue_type_detail', type_id=revenue_type.id)

        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء تحديث نوع الإيراد: {e}')

    return render(request, 'definitions/revenue_type_form.html', {
        'action': 'edit',
        'revenue_type': revenue_type
    })

@login_required
def revenue_type_delete(request, type_id):
    """حذف نوع إيراد"""
    revenue_type = get_object_or_404(RevenueType, id=type_id)

    if request.method == 'POST':
        type_name = revenue_type.name
        revenue_type.delete()
        messages.success(request, f'تم حذف نوع الإيراد "{type_name}" بنجاح!')
        return redirect('definitions:revenue_type_list')

    return render(request, 'definitions/revenue_type_confirm_delete.html', {'revenue_type': revenue_type})

@login_required
def revenue_type_quick_delete(request, type_id):
    """حذف سريع لنوع الإيراد"""
    if request.method == 'POST':
        revenue_type = get_object_or_404(RevenueType, id=type_id)
        type_name = revenue_type.name
        revenue_type.delete()
        messages.success(request, f'تم حذف نوع الإيراد "{type_name}" بنجاح!')

    return redirect('definitions:revenue_type_list')


# ===== أسماء الإيرادات =====

@login_required
def revenue_name_list(request):
    """قائمة أسماء الإيرادات"""
    # الحصول على جميع أسماء الإيرادات
    revenue_names = RevenueName.objects.select_related('revenue_type', 'created_by').all().order_by('name')

    # إحصائيات
    total_names = revenue_names.count()
    active_names = revenue_names.filter(is_active=True).count()
    with_type_count = revenue_names.exclude(revenue_type__isnull=True).count()

    context = {
        'revenue_names': revenue_names,
        'total_names': total_names,
        'active_names': active_names,
        'with_type_count': with_type_count,
    }

    return render(request, 'definitions/revenue_name_list.html', context)

@login_required
def revenue_name_create(request):
    """إضافة اسم إيراد جديد"""
    if request.method == 'POST':
        try:
            code = request.POST.get('code')
            name = request.POST.get('name')
            name_en = request.POST.get('name_en', '')
            revenue_type_id = request.POST.get('revenue_type')
            description = request.POST.get('description', '')
            notes = request.POST.get('notes', '')
            is_active = request.POST.get('is_active') == 'on'

            # التحقق من عدم تكرار الكود
            if RevenueName.objects.filter(code=code).exists():
                messages.error(request, f'كود اسم الإيراد "{code}" موجود بالفعل.')
                return render(request, 'definitions/revenue_name_form.html', {
                    'action': 'create',
                    'form_data': request.POST,
                    'revenue_types': RevenueType.objects.filter(is_active=True).order_by('name')
                })

            # إنشاء اسم الإيراد
            revenue_name_obj = RevenueName.objects.create(
                code=code,
                name=name,
                name_en=name_en,
                revenue_type_id=revenue_type_id if revenue_type_id else None,
                description=description,
                notes=notes,
                is_active=is_active,
                created_by=request.user
            )

            messages.success(request, f'تم إضافة اسم الإيراد "{revenue_name_obj.name}" بنجاح!')
            return redirect('definitions:revenue_name_detail', name_id=revenue_name_obj.id)

        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء إضافة اسم الإيراد: {e}')

    context = {
        'action': 'create',
        'revenue_types': RevenueType.objects.filter(is_active=True).order_by('name')
    }
    return render(request, 'definitions/revenue_name_form.html', context)

@login_required
def revenue_name_detail(request, name_id):
    """تفاصيل اسم الإيراد"""
    revenue_name = get_object_or_404(RevenueName, id=name_id)
    return render(request, 'definitions/revenue_name_detail.html', {'revenue_name': revenue_name})

@login_required
def revenue_name_edit(request, name_id):
    """تعديل اسم إيراد موجود"""
    revenue_name = get_object_or_404(RevenueName, id=name_id)

    if request.method == 'POST':
        try:
            code = request.POST.get('code')
            name = request.POST.get('name')
            name_en = request.POST.get('name_en', '')
            revenue_type_id = request.POST.get('revenue_type')
            description = request.POST.get('description', '')
            notes = request.POST.get('notes', '')
            is_active = request.POST.get('is_active') == 'on'

            # التحقق من عدم تكرار الكود (باستثناء الاسم الحالي)
            if RevenueName.objects.filter(code=code).exclude(id=revenue_name.id).exists():
                messages.error(request, f'كود اسم الإيراد "{code}" موجود بالفعل.')
                return render(request, 'definitions/revenue_name_form.html', {
                    'action': 'edit',
                    'revenue_name': revenue_name,
                    'form_data': request.POST,
                    'revenue_types': RevenueType.objects.filter(is_active=True).order_by('name')
                })

            # تحديث البيانات
            revenue_name.code = code
            revenue_name.name = name
            revenue_name.name_en = name_en
            revenue_name.revenue_type_id = revenue_type_id if revenue_type_id else None
            revenue_name.description = description
            revenue_name.notes = notes
            revenue_name.is_active = is_active
            revenue_name.save()

            messages.success(request, f'تم تحديث اسم الإيراد "{revenue_name.name}" بنجاح!')
            return redirect('definitions:revenue_name_detail', name_id=revenue_name.id)

        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء تحديث اسم الإيراد: {e}')

    context = {
        'action': 'edit',
        'revenue_name': revenue_name,
        'revenue_types': RevenueType.objects.filter(is_active=True).order_by('name')
    }
    return render(request, 'definitions/revenue_name_form.html', context)

@login_required
def revenue_name_delete(request, name_id):
    """حذف اسم إيراد"""
    revenue_name = get_object_or_404(RevenueName, id=name_id)

    if request.method == 'POST':
        name_value = revenue_name.name
        revenue_name.delete()
        messages.success(request, f'تم حذف اسم الإيراد "{name_value}" بنجاح!')
        return redirect('definitions:revenue_name_list')

    return render(request, 'definitions/revenue_name_confirm_delete.html', {'revenue_name': revenue_name})

@login_required
def revenue_name_quick_delete(request, name_id):
    """حذف سريع لاسم الإيراد"""
    if request.method == 'POST':
        revenue_name = get_object_or_404(RevenueName, id=name_id)
        name_value = revenue_name.name
        revenue_name.delete()
        messages.success(request, f'تم حذف اسم الإيراد "{name_value}" بنجاح!')

    return redirect('definitions:revenue_name_list')

# ===== تعريف الأصناف =====
@login_required
def product_list(request):
    """قائمة تعريف الأصناف"""
    search_query = request.GET.get('search', '')
    category_id = request.GET.get('category', '')

    products = ProductDefinition.objects.all()

    if search_query:
        products = products.filter(
            Q(name__icontains=search_query) |
            Q(code__icontains=search_query) |
            Q(barcode__icontains=search_query) |
            Q(description__icontains=search_query)
        )

    if category_id:
        products = products.filter(category_id=category_id)

    products = products.select_related('category').order_by('code')

    paginator = Paginator(products, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # الحصول على الفئات للفلترة
    categories = ProductCategory.objects.filter(is_active=True).order_by('name')

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'category_id': category_id,
        'categories': categories,
        'total_products': ProductDefinition.objects.count(),
        'active_products': ProductDefinition.objects.filter(is_active=True).count(),
        'low_stock_products': ProductDefinition.objects.filter(minimum_stock__gt=0).count(),
        'out_of_stock_products': 0,  # مؤقتاً حتى نضيف نظام المخزون
    }

    return render(request, 'definitions/product_list.html', context)

# تم حذف الدالة المكررة - استخدم الدالة الأولى التي تستخدم Django forms

@login_required
def product_edit(request, product_id):
    """تعديل صنف موجود"""
    product = get_object_or_404(ProductDefinition, id=product_id)

    if request.method == 'POST':
        code = request.POST.get('code')
        name = request.POST.get('name')
        name_en = request.POST.get('name_en', '')
        barcode = request.POST.get('barcode', '')
        category_id = request.POST.get('category')
        main_unit = request.POST.get('unit')
        product_type = request.POST.get('product_type', 'product')
        description = request.POST.get('description', '')
        cost_price = request.POST.get('cost_price', '0')
        selling_price = request.POST.get('selling_price', '0')
        minimum_stock = request.POST.get('min_stock', '0')
        maximum_stock = request.POST.get('max_stock', '0')
        is_active = request.POST.get('is_active') == 'on'
        track_inventory = request.POST.get('track_inventory') == 'on'

        # التحقق من عدم تكرار الكود
        if ProductDefinition.objects.filter(code=code).exclude(id=product.id).exists():
            messages.error(request, f'كود الصنف "{code}" موجود بالفعل.')
            return render(request, 'definitions/product_form.html', {
                'product': product,
                'action': 'edit',
                'form_data': request.POST,
                'categories': ProductCategory.objects.filter(is_active=True),
                'units': ['قطعة', 'كيلو', 'متر', 'لتر', 'علبة', 'كرتونة']
            })

        # التحقق من عدم تكرار الباركود
        if barcode and ProductDefinition.objects.filter(barcode=barcode).exclude(id=product.id).exists():
            messages.error(request, f'الباركود "{barcode}" موجود بالفعل.')
            return render(request, 'definitions/product_form.html', {
                'product': product,
                'action': 'edit',
                'form_data': request.POST,
                'categories': ProductCategory.objects.filter(is_active=True),
                'units': ['قطعة', 'كيلو', 'متر', 'لتر', 'علبة', 'كرتونة']
            })

        # الحصول على الفئة
        category = None
        if category_id:
            try:
                category = ProductCategory.objects.get(id=category_id)
            except ProductCategory.DoesNotExist:
                pass

        product.code = code
        product.name = name
        product.name_en = name_en
        product.barcode = barcode
        product.category = category
        product.main_unit = main_unit
        product.product_type = product_type
        product.description = description
        product.cost_price = cost_price
        product.selling_price = selling_price
        product.minimum_stock = minimum_stock
        product.maximum_stock = maximum_stock
        product.is_active = is_active
        product.track_inventory = track_inventory
        product.save()

        messages.success(request, f'تم تحديث الصنف "{product.name}" بنجاح!')
        return redirect('definitions:product_list')

    context = {
        'product': product,
        'action': 'edit',
        'categories': ProductCategory.objects.filter(is_active=True),
        'units': ['قطعة', 'كيلو', 'متر', 'لتر', 'علبة', 'كرتونة', 'جرام', 'طن', 'سم', 'مم']
    }

    return render(request, 'definitions/product_form.html', context)





@login_required
def product_quick_delete(request, product_id):
    """حذف سريع للصنف"""
    if request.method == 'POST':
        product = get_object_or_404(ProductDefinition, id=product_id)
        product_name = product.name
        product.delete()
        messages.success(request, f'تم حذف الصنف "{product_name}" بنجاح!')
        return redirect('definitions:product_list')

    return redirect('definitions:product_list')


# ===== أكواد الأصناف =====
@login_required
def product_code_list(request):
    """قائمة أكواد الأصناف"""
    from .models import ProductCode

    search_query = request.GET.get('search', '')
    code_type = request.GET.get('code_type', '')
    product_id = request.GET.get('product', '')

    codes = ProductCode.objects.select_related('product', 'created_by').all()

    if search_query:
        codes = codes.filter(
            Q(code__icontains=search_query) |
            Q(product__name__icontains=search_query) |
            Q(product__code__icontains=search_query) |
            Q(supplier_name__icontains=search_query) |
            Q(description__icontains=search_query)
        )

    if code_type:
        codes = codes.filter(code_type=code_type)

    if product_id:
        codes = codes.filter(product_id=product_id)

    codes = codes.order_by('product__code', 'code_type', 'code')

    paginator = Paginator(codes, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # الحصول على الأصناف والأنواع للفلترة
    products = ProductDefinition.objects.filter(is_active=True).order_by('code')
    code_types = ProductCode.CODE_TYPES

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'code_type': code_type,
        'product_id': product_id,
        'products': products,
        'code_types': code_types,
    }

    return render(request, 'definitions/product_code_list.html', context)

@login_required
def product_code_create(request):
    """إنشاء كود صنف جديد"""
    from .models import ProductCode

    if request.method == 'POST':
        product_id = request.POST.get('product')
        code_type = request.POST.get('code_type')
        code = request.POST.get('code')
        description = request.POST.get('description', '')
        supplier_name = request.POST.get('supplier_name', '')
        reference_number = request.POST.get('reference_number', '')
        is_active = request.POST.get('is_active') == 'on'
        is_primary = request.POST.get('is_primary') == 'on'

        # التحقق من الحقول المطلوبة
        if not all([product_id, code_type, code]):
            messages.error(request, 'يرجى ملء جميع الحقول المطلوبة.')
            return render(request, 'definitions/product_code_form.html', {
                'form_data': request.POST,
                'products': ProductDefinition.objects.filter(is_active=True).order_by('code'),
                'code_types': ProductCode.CODE_TYPES,
            })

        # التحقق من عدم تكرار الكود
        if ProductCode.objects.filter(code_type=code_type, code=code).exists():
            messages.error(request, f'الكود "{code}" من نوع "{dict(ProductCode.CODE_TYPES)[code_type]}" موجود بالفعل.')
            return render(request, 'definitions/product_code_form.html', {
                'form_data': request.POST,
                'products': ProductDefinition.objects.filter(is_active=True).order_by('code'),
                'code_types': ProductCode.CODE_TYPES,
            })

        try:
            product = ProductDefinition.objects.get(id=product_id)

            product_code = ProductCode.objects.create(
                product=product,
                code_type=code_type,
                code=code,
                description=description,
                supplier_name=supplier_name,
                reference_number=reference_number,
                is_active=is_active,
                is_primary=is_primary,
                created_by=request.user
            )

            messages.success(request, f'تم إنشاء كود الصنف "{product_code.code}" بنجاح!')
            return redirect('definitions:product_code_list')

        except ProductDefinition.DoesNotExist:
            messages.error(request, 'الصنف المحدد غير موجود.')
        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء إنشاء كود الصنف: {e}')

    context = {
        'action': 'create',
        'products': ProductDefinition.objects.filter(is_active=True).order_by('code'),
        'code_types': ProductCode.CODE_TYPES,
    }

    return render(request, 'definitions/product_code_form.html', context)

@login_required
def product_code_edit(request, code_id):
    """تعديل كود صنف"""
    from .models import ProductCode

    product_code = get_object_or_404(ProductCode, id=code_id)

    if request.method == 'POST':
        product_id = request.POST.get('product')
        code_type = request.POST.get('code_type')
        code = request.POST.get('code')
        description = request.POST.get('description', '')
        supplier_name = request.POST.get('supplier_name', '')
        reference_number = request.POST.get('reference_number', '')
        is_active = request.POST.get('is_active') == 'on'
        is_primary = request.POST.get('is_primary') == 'on'

        # التحقق من الحقول المطلوبة
        if not all([product_id, code_type, code]):
            messages.error(request, 'يرجى ملء جميع الحقول المطلوبة.')
            return render(request, 'definitions/product_code_form.html', {
                'product_code': product_code,
                'action': 'edit',
                'form_data': request.POST,
                'products': ProductDefinition.objects.filter(is_active=True).order_by('code'),
                'code_types': ProductCode.CODE_TYPES,
            })

        # التحقق من عدم تكرار الكود
        if ProductCode.objects.filter(code_type=code_type, code=code).exclude(id=product_code.id).exists():
            messages.error(request, f'الكود "{code}" من نوع "{dict(ProductCode.CODE_TYPES)[code_type]}" موجود بالفعل.')
            return render(request, 'definitions/product_code_form.html', {
                'product_code': product_code,
                'action': 'edit',
                'form_data': request.POST,
                'products': ProductDefinition.objects.filter(is_active=True).order_by('code'),
                'code_types': ProductCode.CODE_TYPES,
            })

        try:
            product = ProductDefinition.objects.get(id=product_id)

            product_code.product = product
            product_code.code_type = code_type
            product_code.code = code
            product_code.description = description
            product_code.supplier_name = supplier_name
            product_code.reference_number = reference_number
            product_code.is_active = is_active
            product_code.is_primary = is_primary
            product_code.save()

            messages.success(request, f'تم تحديث كود الصنف "{product_code.code}" بنجاح!')
            return redirect('definitions:product_code_list')

        except ProductDefinition.DoesNotExist:
            messages.error(request, 'الصنف المحدد غير موجود.')
        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء تحديث كود الصنف: {e}')

    context = {
        'product_code': product_code,
        'action': 'edit',
        'products': ProductDefinition.objects.filter(is_active=True).order_by('code'),
        'code_types': ProductCode.CODE_TYPES,
    }

    return render(request, 'definitions/product_code_form.html', context)

@login_required
def product_code_delete(request, code_id):
    """حذف كود صنف"""
    from .models import ProductCode

    product_code = get_object_or_404(ProductCode, id=code_id)

    if request.method == 'POST':
        code_name = product_code.code
        product_name = product_code.product.name
        product_code.delete()
        messages.success(request, f'تم حذف كود الصنف "{code_name}" للصنف "{product_name}" بنجاح!')
        return redirect('definitions:product_code_list')

    context = {
        'product_code': product_code,
    }

    return render(request, 'definitions/product_code_confirm_delete.html', context)

@login_required
def product_code_detail(request, code_id):
    """تفاصيل كود صنف"""
    from .models import ProductCode

    product_code = get_object_or_404(ProductCode, id=code_id)

    context = {
        'product_code': product_code,
    }

    return render(request, 'definitions/product_code_detail.html', context)


# ===== تعريف الأشخاص =====
@login_required
def person_list(request):
    """قائمة تعريف الأشخاص"""
    search_query = request.GET.get('search', '')
    person_type = request.GET.get('person_type', '')

    persons = PersonDefinition.objects.all()

    if search_query:
        persons = persons.filter(
            Q(name__icontains=search_query) |
            Q(code__icontains=search_query) |
            Q(phone__icontains=search_query) |
            Q(email__icontains=search_query) |
            Q(national_id__icontains=search_query)
        )

    if person_type:
        persons = persons.filter(person_type=person_type)

    persons = persons.order_by('code')

    paginator = Paginator(persons, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # الحصول على أنواع الأشخاص للفلترة
    person_types = PersonDefinition.PERSON_TYPES

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'person_type': person_type,
        'person_types': person_types,
        'total_persons': PersonDefinition.objects.count(),
        'active_persons': PersonDefinition.objects.filter(is_active=True).count(),
        'customers_count': PersonDefinition.objects.filter(person_type='customer').count(),
        'suppliers_count': PersonDefinition.objects.filter(person_type='supplier').count(),
        'employees_count': PersonDefinition.objects.filter(person_type='employee').count(),
    }

    return render(request, 'definitions/person_list.html', context)

def person_create(request):
    """إنشاء شخص جديد"""
    print("="*50)
    print(f"🚀 person_create called with method: {request.method}")
    print(f"👤 User: {request.user}")
    print(f"🔐 User authenticated: {request.user.is_authenticated}")
    print(f"📍 Request path: {request.get_full_path()}")
    print(f"🌐 Request META: {request.META.get('HTTP_USER_AGENT', 'Unknown')}")

    if request.method == 'POST':
        print("📤 Processing POST request")
        print(f"📝 POST data keys: {list(request.POST.keys())}")
        print(f"📝 POST data: {dict(request.POST)}")

        # إذا لم يكن المستخدم مسجل دخول، إعادة توجيه لتسجيل الدخول
        if not request.user.is_authenticated:
            print("❌ User not authenticated, redirecting to login")
            from django.contrib.auth.views import redirect_to_login
            return redirect_to_login(request.get_full_path())

        # الحصول على البيانات من النموذج
        code = request.POST.get('code', '').strip()
        name = request.POST.get('name', '').strip()
        name_en = request.POST.get('name_en', '').strip()
        person_type = request.POST.get('person_type', '').strip()
        gender = request.POST.get('gender', '').strip()
        birth_date = request.POST.get('birth_date', '').strip()
        national_id = request.POST.get('national_id', '').strip()
        passport_number = request.POST.get('passport_number', '').strip()
        phone = request.POST.get('phone', '').strip()
        mobile = request.POST.get('mobile', '').strip()
        email = request.POST.get('email', '').strip()
        address = request.POST.get('address', '').strip()
        city = request.POST.get('city', '').strip()
        state = request.POST.get('state', '').strip()
        country = request.POST.get('country', '').strip()
        postal_code = request.POST.get('postal_code', '').strip()
        credit_limit = request.POST.get('credit_limit', '0')
        payment_terms = request.POST.get('payment_terms', '0')
        currency_id = request.POST.get('currency', '')
        tax_number = request.POST.get('tax_number', '').strip()
        tax_rate = request.POST.get('tax_rate', '0')
        notes = request.POST.get('notes', '').strip()
        is_active = request.POST.get('is_active') == 'on'

        print(f"Extracted data: code={code}, name={name}, person_type={person_type}")

        # التحقق من الحقول المطلوبة
        if not all([code, name, person_type]):
            messages.error(request, 'يرجى ملء جميع الحقول المطلوبة.')
            try:
                currencies = CurrencyDefinition.objects.filter(is_active=True)
            except:
                currencies = []
            return render(request, 'definitions/person_form.html', {
                'form_data': request.POST,
                'person_types': PersonDefinition.PERSON_TYPES,
                'gender_choices': PersonDefinition.GENDER_CHOICES,
                'currencies': currencies,
            })

        # التحقق من عدم تكرار الكود
        if PersonDefinition.objects.filter(code=code).exists():
            messages.error(request, f'كود الشخص "{code}" موجود بالفعل.')
            try:
                currencies = CurrencyDefinition.objects.filter(is_active=True)
            except:
                currencies = []
            return render(request, 'definitions/person_form.html', {
                'form_data': request.POST,
                'person_types': PersonDefinition.PERSON_TYPES,
                'gender_choices': PersonDefinition.GENDER_CHOICES,
                'currencies': currencies,
            })

        try:
            print("🔄 Starting person creation...")
            print(f"📊 Required fields check:")
            print(f"   - Code: '{code}' (length: {len(code)})")
            print(f"   - Name: '{name}' (length: {len(name)})")
            print(f"   - Person Type: '{person_type}'")

            # التحقق من الحقول المطلوبة
            if not code:
                raise ValueError("كود الشخص مطلوب")
            if not name:
                raise ValueError("اسم الشخص مطلوب")
            if not person_type:
                raise ValueError("نوع الشخص مطلوب")

            print("✅ Required fields validation passed")

            # تحويل التواريخ والأرقام بأمان
            birth_date_obj = None
            if birth_date:
                try:
                    from datetime import datetime
                    birth_date_obj = datetime.strptime(birth_date, '%Y-%m-%d').date()
                    print(f"📅 Birth date converted: {birth_date_obj}")
                except ValueError as e:
                    print(f"❌ Birth date conversion error: {e}")
                    birth_date_obj = None

            try:
                credit_limit = float(credit_limit) if credit_limit else 0.0
                print(f"💰 Credit limit: {credit_limit}")
            except (ValueError, TypeError) as e:
                print(f"❌ Credit limit conversion error: {e}")
                credit_limit = 0.0

            try:
                payment_terms = int(payment_terms) if payment_terms else 0
                print(f"📅 Payment terms: {payment_terms}")
            except (ValueError, TypeError) as e:
                print(f"❌ Payment terms conversion error: {e}")
                payment_terms = 0

            try:
                tax_rate = float(tax_rate) if tax_rate else 0.0
                print(f"📊 Tax rate: {tax_rate}")
            except (ValueError, TypeError) as e:
                print(f"❌ Tax rate conversion error: {e}")
                tax_rate = 0.0

            # معالجة العملة
            currency_obj = None
            if currency_id:
                try:
                    currency_obj = CurrencyDefinition.objects.get(id=currency_id)
                    print(f"💱 Currency found: {currency_obj.name}")
                except CurrencyDefinition.DoesNotExist:
                    print(f"❌ Currency with id {currency_id} not found")
                except Exception as e:
                    print(f"❌ Currency lookup error: {e}")
            else:
                print("💱 No currency specified")

            print(f"🏗️ Creating person with:")
            print(f"   - Code: {code}")
            print(f"   - Name: {name}")
            print(f"   - Type: {person_type}")
            print(f"   - Currency: {currency_obj}")
            print(f"   - User: {request.user}")

            print("💾 Attempting to save person to database...")

            person = PersonDefinition.objects.create(
                code=code,
                name=name,
                name_en=name_en or '',
                person_type=person_type,
                gender=gender or '',
                birth_date=birth_date_obj,
                national_id=national_id or '',
                passport_number=passport_number or '',
                phone=phone or '',
                mobile=mobile or '',
                email=email or '',
                address=address or '',
                city=city or '',
                state=state or '',
                country=country or '',
                postal_code=postal_code or '',
                credit_limit=credit_limit,
                payment_terms=payment_terms,
                currency=currency_obj,
                tax_number=tax_number or '',
                tax_rate=tax_rate,
                notes=notes or '',
                is_active=is_active,
                created_by=request.user
            )

            print(f"✅ Person created successfully!")
            print(f"   - ID: {person.id}")
            print(f"   - Code: {person.code}")
            print(f"   - Name: {person.name}")
            print(f"   - Type: {person.person_type}")

            messages.success(request, f'تم إنشاء تعريف الشخص "{person.name}" بنجاح!')
            print("🔄 Redirecting to person_list")
            return redirect('definitions:person_list')

        except Exception as e:
            print("="*50)
            print(f"❌ CRITICAL ERROR creating person: {e}")
            print(f"❌ Error type: {type(e).__name__}")
            import traceback
            print("❌ Full traceback:")
            traceback.print_exc()
            print("="*50)

            from dashboard.context_processors import user_settings
            user_context = user_settings(request)
            user_language = user_context.get('user_language', 'ar')

            if user_language == 'en':
                error_message = f'An error occurred while creating person definition: {e}'
            else:
                error_message = f'حدث خطأ أثناء إنشاء تعريف الشخص: {e}'
            messages.error(request, error_message)

            try:
                currencies = CurrencyDefinition.objects.filter(is_active=True)
                print(f"📊 Found {currencies.count()} currencies for error form")
            except Exception as curr_error:
                print(f"❌ Error getting currencies for error form: {curr_error}")
                currencies = []

            print("🔄 Returning to form with error")
            return render(request, 'definitions/person_form.html', {
                'action': 'create',
                'form_data': request.POST,
                'person_types': PersonDefinition.PERSON_TYPES,
                'gender_choices': PersonDefinition.GENDER_CHOICES,
                'currencies': currencies,
                'error_message': error_message,
            })

    # معالجة GET request
    print("Processing GET request - showing form")

    # إذا لم يكن المستخدم مسجل دخول، إعادة توجيه لتسجيل الدخول
    if not request.user.is_authenticated:
        print("User not authenticated for GET, redirecting to login")
        from django.contrib.auth.views import redirect_to_login
        return redirect_to_login(request.get_full_path())

    # الحصول على العملات بأمان
    try:
        currencies = CurrencyDefinition.objects.filter(is_active=True)
        print(f"Found {currencies.count()} currencies")
    except Exception as e:
        print(f"Error getting currencies: {e}")
        currencies = []

    context = {
        'action': 'create',
        'person_types': PersonDefinition.PERSON_TYPES,
        'gender_choices': PersonDefinition.GENDER_CHOICES,
        'currencies': currencies,
    }

    print("Rendering person_form.html")
    return render(request, 'definitions/person_form.html', context)

@login_required
def person_edit(request, person_id):
    """تعديل شخص"""
    person = get_object_or_404(PersonDefinition, id=person_id)

    if request.method == 'POST':
        code = request.POST.get('code')
        name = request.POST.get('name')
        name_en = request.POST.get('name_en', '')
        person_type = request.POST.get('person_type')
        gender = request.POST.get('gender', '')
        birth_date = request.POST.get('birth_date', '')
        national_id = request.POST.get('national_id', '')
        passport_number = request.POST.get('passport_number', '')
        phone = request.POST.get('phone', '')
        mobile = request.POST.get('mobile', '')
        email = request.POST.get('email', '')
        address = request.POST.get('address', '')
        city = request.POST.get('city', '')
        state = request.POST.get('state', '')
        country = request.POST.get('country', '')
        postal_code = request.POST.get('postal_code', '')
        credit_limit = request.POST.get('credit_limit', 0)
        payment_terms = request.POST.get('payment_terms', 0)
        tax_number = request.POST.get('tax_number', '')
        tax_rate = request.POST.get('tax_rate', 0)
        notes = request.POST.get('notes', '')
        is_active = request.POST.get('is_active') == 'on'

        # التحقق من الحقول المطلوبة
        if not all([code, name, person_type]):
            messages.error(request, 'يرجى ملء جميع الحقول المطلوبة.')
            return render(request, 'definitions/person_form.html', {
                'person': person,
                'action': 'edit',
                'form_data': request.POST,
                'person_types': PersonDefinition.PERSON_TYPES,
                'gender_choices': PersonDefinition.GENDER_CHOICES,
                'currencies': CurrencyDefinition.objects.filter(is_active=True),
            })

        # التحقق من عدم تكرار الكود
        if PersonDefinition.objects.filter(code=code).exclude(id=person.id).exists():
            messages.error(request, f'كود الشخص "{code}" موجود بالفعل.')
            return render(request, 'definitions/person_form.html', {
                'person': person,
                'action': 'edit',
                'form_data': request.POST,
                'person_types': PersonDefinition.PERSON_TYPES,
                'gender_choices': PersonDefinition.GENDER_CHOICES,
                'currencies': CurrencyDefinition.objects.filter(is_active=True),
            })

        try:
            # تحويل التواريخ والأرقام
            birth_date_obj = None
            if birth_date:
                from datetime import datetime
                birth_date_obj = datetime.strptime(birth_date, '%Y-%m-%d').date()

            credit_limit = float(credit_limit) if credit_limit else 0
            payment_terms = int(payment_terms) if payment_terms else 0
            tax_rate = float(tax_rate) if tax_rate else 0

            person.code = code
            person.name = name
            person.name_en = name_en
            person.person_type = person_type
            person.gender = gender
            person.birth_date = birth_date_obj
            person.national_id = national_id
            person.passport_number = passport_number
            person.phone = phone
            person.mobile = mobile
            person.email = email
            person.address = address
            person.city = city
            person.state = state
            person.country = country
            person.postal_code = postal_code
            person.credit_limit = credit_limit
            person.payment_terms = payment_terms
            person.tax_number = tax_number
            person.tax_rate = tax_rate
            person.notes = notes
            person.is_active = is_active
            person.save()

            messages.success(request, f'تم تحديث تعريف الشخص "{person.name}" بنجاح!')
            return redirect('definitions:person_list')

        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء تحديث تعريف الشخص: {e}')

    context = {
        'person': person,
        'action': 'edit',
        'person_types': PersonDefinition.PERSON_TYPES,
        'gender_choices': PersonDefinition.GENDER_CHOICES,
        'currencies': CurrencyDefinition.objects.filter(is_active=True),
    }

    return render(request, 'definitions/person_form.html', context)

@login_required
def person_delete(request, person_id):
    """حذف شخص"""
    person = get_object_or_404(PersonDefinition, id=person_id)

    if request.method == 'POST':
        person_name = person.name
        person.delete()
        messages.success(request, f'تم حذف تعريف الشخص "{person_name}" بنجاح!')
        return redirect('definitions:person_list')

    context = {
        'person': person,
    }

    return render(request, 'definitions/person_confirm_delete.html', context)

@login_required
def person_detail(request, person_id):
    """تفاصيل شخص"""
    person = get_object_or_404(PersonDefinition, id=person_id)

    context = {
        'person': person,
    }

    return render(request, 'definitions/person_detail.html', context)

# ===== فئات الأصناف =====
@login_required
def category_list(request):
    """قائمة فئات الأصناف"""
    search_query = request.GET.get('search', '')

    categories = ProductCategory.objects.all()

    if search_query:
        categories = categories.filter(
            Q(name__icontains=search_query) |
            Q(code__icontains=search_query) |
            Q(description__icontains=search_query)
        )

    categories = categories.order_by('code')

    paginator = Paginator(categories, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'total_categories': ProductCategory.objects.count(),
    }

    return render(request, 'definitions/category_list.html', context)

@login_required
def category_create(request):
    """إنشاء فئة أصناف جديدة"""
    if request.method == 'POST':
        code = request.POST.get('code')
        name = request.POST.get('name')
        description = request.POST.get('description', '')
        parent_id = request.POST.get('parent_category')
        is_active = request.POST.get('is_active') == 'on'

        # التحقق من عدم تكرار الكود
        if ProductCategory.objects.filter(code=code).exists():
            messages.error(request, f'كود الفئة "{code}" موجود بالفعل.')
            return render(request, 'definitions/category_form.html', {
                'form_data': request.POST,
                'parent_categories': ProductCategory.objects.filter(parent__isnull=True, is_active=True)
            })

        # الحصول على الفئة الأب إذا تم تحديدها
        parent_category = None
        if parent_id:
            try:
                parent_category = ProductCategory.objects.get(id=parent_id)
            except ProductCategory.DoesNotExist:
                pass

        category = ProductCategory.objects.create(
            code=code,
            name=name,
            description=description,
            parent=parent_category,
            is_active=is_active,
            created_by=request.user
        )

        messages.success(request, f'تم إنشاء فئة الأصناف "{category.name}" بنجاح!')
        return redirect('definitions:category_list')

    context = {
        'action': 'create',
        'parent_categories': ProductCategory.objects.filter(parent__isnull=True, is_active=True)
    }

    return render(request, 'definitions/category_form.html', context)

@login_required
def category_edit(request, category_id):
    """تعديل فئة أصناف موجودة"""
    category = get_object_or_404(ProductCategory, id=category_id)

    if request.method == 'POST':
        code = request.POST.get('code')
        name = request.POST.get('name')
        description = request.POST.get('description', '')
        parent_id = request.POST.get('parent_category')
        is_active = request.POST.get('is_active') == 'on'

        # التحقق من عدم تكرار الكود
        if ProductCategory.objects.filter(code=code).exclude(id=category.id).exists():
            messages.error(request, f'كود الفئة "{code}" موجود بالفعل.')
            return render(request, 'definitions/category_form.html', {
                'category': category,
                'action': 'edit',
                'form_data': request.POST,
                'parent_categories': ProductCategory.objects.filter(parent__isnull=True, is_active=True).exclude(id=category.id)
            })

        # الحصول على الفئة الأب إذا تم تحديدها
        parent_category = None
        if parent_id:
            try:
                parent_category = ProductCategory.objects.get(id=parent_id)
                # منع جعل الفئة أب لنفسها
                if parent_category == category:
                    messages.error(request, 'لا يمكن جعل الفئة أب لنفسها.')
                    return render(request, 'definitions/category_form.html', {
                        'category': category,
                        'action': 'edit',
                        'form_data': request.POST,
                        'parent_categories': ProductCategory.objects.filter(parent__isnull=True, is_active=True).exclude(id=category.id)
                    })
            except ProductCategory.DoesNotExist:
                pass

        category.code = code
        category.name = name
        category.description = description
        category.parent = parent_category
        category.is_active = is_active
        category.save()

        messages.success(request, f'تم تحديث فئة الأصناف "{category.name}" بنجاح!')
        return redirect('definitions:category_list')

    context = {
        'category': category,
        'action': 'edit',
        'parent_categories': ProductCategory.objects.filter(parent__isnull=True, is_active=True).exclude(id=category.id)
    }

    return render(request, 'definitions/category_form.html', context)

@login_required
def category_detail(request, category_id):
    """تفاصيل فئة الأصناف"""
    category = get_object_or_404(ProductCategory, id=category_id)

    # الحصول على الفئات الفرعية
    subcategories = ProductCategory.objects.filter(parent=category)

    # الحصول على الأصناف في هذه الفئة
    products = ProductDefinition.objects.filter(category=category)

    context = {
        'category': category,
        'subcategories': subcategories,
        'products': products,
    }

    return render(request, 'definitions/category_detail.html', context)

@login_required
def category_delete(request, category_id):
    """حذف فئة أصناف"""
    category = get_object_or_404(ProductCategory, id=category_id)

    if request.method == 'POST':
        # التحقق من وجود فئات فرعية
        subcategories_count = ProductCategory.objects.filter(parent=category).count()
        products_count = ProductDefinition.objects.filter(category=category).count()

        if subcategories_count > 0 or products_count > 0:
            messages.error(
                request,
                f'لا يمكن حذف الفئة "{category.name}" لأنها تحتوي على '
                f'({subcategories_count} فئة فرعية، {products_count} صنف). '
                'يرجى حذف البيانات المرتبطة أولاً أو إلغاء تفعيل الفئة.'
            )
            return redirect('definitions:category_detail', category_id=category.id)

        category_name = category.name
        category.delete()
        messages.success(request, f'تم حذف فئة الأصناف "{category_name}" بنجاح!')
        return redirect('definitions:category_list')

    context = {
        'category': category,
        'subcategories_count': ProductCategory.objects.filter(parent=category).count(),
        'products_count': ProductDefinition.objects.filter(category=category).count(),
    }

    return render(request, 'definitions/category_confirm_delete.html', context)

@login_required
def category_quick_delete(request, category_id):
    """حذف سريع لفئة الأصناف"""
    if request.method == 'POST':
        category = get_object_or_404(ProductCategory, id=category_id)

        # التحقق من وجود بيانات مرتبطة
        subcategories_count = ProductCategory.objects.filter(parent=category).count()
        products_count = ProductDefinition.objects.filter(category=category).count()

        if subcategories_count > 0 or products_count > 0:
            messages.error(
                request,
                f'لا يمكن حذف الفئة "{category.name}" لأنها تحتوي على بيانات مرتبطة. '
                'يرجى استخدام صفحة التفاصيل للحذف الآمن.'
            )
        else:
            category_name = category.name
            category.delete()
            messages.success(request, f'تم حذف فئة الأصناف "{category_name}" بنجاح!')

        return redirect('definitions:category_list')

    return redirect('definitions:category_list')


# ===== مراكز الربحية =====

@login_required
def profit_center_list(request):
    """قائمة مراكز الربحية"""
    profit_centers = ProfitCenter.objects.select_related('parent', 'created_by').all()

    # إحصائيات
    total_centers = profit_centers.count()
    active_centers = profit_centers.filter(is_active=True).count()
    main_centers = profit_centers.filter(parent__isnull=True).count()
    sub_centers = profit_centers.filter(parent__isnull=False).count()

    context = {
        'profit_centers': profit_centers,
        'total_centers': total_centers,
        'active_centers': active_centers,
        'main_centers': main_centers,
        'sub_centers': sub_centers,
    }
    return render(request, 'definitions/profit_center_list.html', context)

@login_required
def profit_center_create(request):
    """إضافة مركز ربحية جديد"""
    if request.method == 'POST':
        # جمع البيانات من النموذج
        code = request.POST.get('code', '').strip().upper()
        name = request.POST.get('name', '').strip()
        name_en = request.POST.get('name_en', '').strip()
        parent_id = request.POST.get('parent')
        description = request.POST.get('description', '').strip()
        notes = request.POST.get('notes', '').strip()
        is_active = request.POST.get('is_active') == 'on'

        # التحقق من صحة البيانات
        if not code or not name:
            messages.error(request, 'يرجى ملء جميع الحقول المطلوبة!')
            return render(request, 'definitions/profit_center_form.html', {
                'action': 'create',
                'form_data': request.POST,
                'parent_centers': ProfitCenter.objects.filter(parent__isnull=True, is_active=True)
            })

        # التحقق من عدم تكرار الكود
        if ProfitCenter.objects.filter(code=code).exists():
            messages.error(request, f'كود مركز الربحية "{code}" موجود مسبقاً!')
            return render(request, 'definitions/profit_center_form.html', {
                'action': 'create',
                'form_data': request.POST,
                'parent_centers': ProfitCenter.objects.filter(parent__isnull=True, is_active=True)
            })

        try:
            # إنشاء مركز الربحية
            parent = None
            if parent_id:
                parent = get_object_or_404(ProfitCenter, id=parent_id)

            profit_center = ProfitCenter.objects.create(
                code=code,
                name=name,
                name_en=name_en or '',
                parent=parent,
                description=description or '',
                is_active=is_active,
                created_by=request.user
            )

            messages.success(request, f'تم إنشاء مركز الربحية "{profit_center.name}" بنجاح!')
            return redirect('definitions:profit_center_detail', center_id=profit_center.id)

        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء إنشاء مركز الربحية: {str(e)}')

    context = {
        'action': 'create',
        'parent_centers': ProfitCenter.objects.filter(parent__isnull=True, is_active=True)
    }
    return render(request, 'definitions/profit_center_form.html', context)

@login_required
def profit_center_detail(request, center_id):
    """تفاصيل مركز الربحية"""
    profit_center = get_object_or_404(ProfitCenter, id=center_id)
    context = {'profit_center': profit_center}
    return render(request, 'definitions/profit_center_detail.html', context)

@login_required
def profit_center_edit(request, center_id):
    """تعديل مركز الربحية"""
    profit_center = get_object_or_404(ProfitCenter, id=center_id)

    if request.method == 'POST':
        # جمع البيانات من النموذج
        code = request.POST.get('code', '').strip().upper()
        name = request.POST.get('name', '').strip()
        name_en = request.POST.get('name_en', '').strip()
        parent_id = request.POST.get('parent')
        description = request.POST.get('description', '').strip()
        notes = request.POST.get('notes', '').strip()
        is_active = request.POST.get('is_active') == 'on'

        # التحقق من صحة البيانات
        if not code or not name:
            messages.error(request, 'يرجى ملء جميع الحقول المطلوبة!')
            return render(request, 'definitions/profit_center_form.html', {
                'action': 'edit',
                'profit_center': profit_center,
                'form_data': request.POST,
                'parent_centers': ProfitCenter.objects.filter(parent__isnull=True, is_active=True).exclude(id=center_id)
            })

        # التحقق من عدم تكرار الكود
        if ProfitCenter.objects.filter(code=code).exclude(id=center_id).exists():
            messages.error(request, f'كود مركز الربحية "{code}" موجود مسبقاً!')
            return render(request, 'definitions/profit_center_form.html', {
                'action': 'edit',
                'profit_center': profit_center,
                'form_data': request.POST,
                'parent_centers': ProfitCenter.objects.filter(parent__isnull=True, is_active=True).exclude(id=center_id)
            })

        try:
            # تحديث مركز الربحية
            parent = None
            if parent_id:
                parent = get_object_or_404(ProfitCenter, id=parent_id)
                # التأكد من عدم جعل المركز أب لنفسه
                if parent.id == center_id:
                    messages.error(request, 'لا يمكن جعل مركز الربحية أب لنفسه!')
                    return render(request, 'definitions/profit_center_form.html', {
                        'action': 'edit',
                        'profit_center': profit_center,
                        'form_data': request.POST,
                        'parent_centers': ProfitCenter.objects.filter(parent__isnull=True, is_active=True).exclude(id=center_id)
                    })

            profit_center.code = code
            profit_center.name = name
            profit_center.name_en = name_en or ''
            profit_center.parent = parent
            profit_center.description = description or ''
            profit_center.is_active = is_active
            profit_center.save()

            messages.success(request, f'تم تحديث مركز الربحية "{profit_center.name}" بنجاح!')
            return redirect('definitions:profit_center_detail', center_id=profit_center.id)

        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء تحديث مركز الربحية: {str(e)}')

    context = {
        'action': 'edit',
        'profit_center': profit_center,
        'parent_centers': ProfitCenter.objects.filter(parent__isnull=True, is_active=True).exclude(id=center_id)
    }
    return render(request, 'definitions/profit_center_form.html', context)

@login_required
def profit_center_delete(request, center_id):
    """حذف مركز الربحية"""
    profit_center = get_object_or_404(ProfitCenter, id=center_id)

    if request.method == 'POST':
        profit_center_name = profit_center.name
        profit_center.delete()
        messages.success(request, f'تم حذف مركز الربحية "{profit_center_name}" بنجاح!')
        return redirect('definitions:profit_center_list')

    context = {'profit_center': profit_center}
    return render(request, 'definitions/profit_center_detail.html', context)

@login_required
def profit_center_quick_delete(request, center_id):
    """حذف سريع لمركز الربحية"""
    if request.method == 'POST':
        profit_center = get_object_or_404(ProfitCenter, id=center_id)
        profit_center_name = profit_center.name
        profit_center.delete()
        messages.success(request, f'تم حذف مركز الربحية "{profit_center_name}" بنجاح!')

    return redirect('definitions:profit_center_list')


# ===== تعريف الطابعات =====

@login_required
def printer_list(request):
    """قائمة الطابعات"""
    printers = PrinterDefinition.objects.select_related('created_by').all()

    # إحصائيات
    total_printers = printers.count()
    active_printers = printers.filter(is_active=True).count()
    default_printers = printers.filter(default_for_receipts=True).count()
    network_printers = printers.filter(connection_type='network').count()

    context = {
        'printers': printers,
        'total_printers': total_printers,
        'active_printers': active_printers,
        'default_printers': default_printers,
        'network_printers': network_printers,
    }
    return render(request, 'definitions/printer_list.html', context)

@login_required
def printer_create(request):
    """إضافة طابعة جديدة"""
    if request.method == 'POST':
        # جمع البيانات من النموذج
        name = request.POST.get('name', '').strip()
        description = request.POST.get('description', '').strip()
        printer_type = request.POST.get('printer_type', 'thermal')
        connection_type = request.POST.get('connection_type', 'usb')
        ip_address = request.POST.get('ip_address', '').strip()
        port = request.POST.get('port', '').strip()
        location = request.POST.get('location', '').strip()
        department = request.POST.get('department', '').strip()
        is_active = request.POST.get('is_active') == 'on'
        default_for_receipts = request.POST.get('default_for_receipts') == 'on'

        # التحقق من صحة البيانات
        if not name:
            messages.error(request, 'يرجى ملء جميع الحقول المطلوبة!')
            return render(request, 'definitions/printer_form.html', {
                'action': 'create',
                'form_data': request.POST
            })

        try:
            # إذا كانت الطابعة افتراضية للإيصالات، إلغاء الافتراضية من الطابعات الأخرى
            if default_for_receipts:
                PrinterDefinition.objects.filter(default_for_receipts=True).update(default_for_receipts=False)

            # إنشاء الطابعة
            printer = PrinterDefinition.objects.create(
                name=name,
                description=description or '',
                printer_type=printer_type,
                connection_type=connection_type,
                ip_address=ip_address or None,
                port=int(port) if port else None,
                location=location or '',
                department=department or '',
                default_for_receipts=default_for_receipts,
                is_active=is_active,
                created_by=request.user
            )

            messages.success(request, f'تم إنشاء الطابعة "{printer.name}" بنجاح!')
            return redirect('definitions:printer_detail', printer_id=printer.id)

        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء إنشاء الطابعة: {str(e)}')

    context = {'action': 'create'}
    return render(request, 'definitions/printer_form.html', context)

@login_required
def printer_detail(request, printer_id):
    """تفاصيل الطابعة"""
    printer = get_object_or_404(PrinterDefinition, id=printer_id)
    context = {'printer': printer}
    return render(request, 'definitions/printer_detail.html', context)

@login_required
def printer_edit(request, printer_id):
    """تعديل الطابعة"""
    printer = get_object_or_404(PrinterDefinition, id=printer_id)

    if request.method == 'POST':
        # جمع البيانات من النموذج
        name = request.POST.get('name', '').strip()
        description = request.POST.get('description', '').strip()
        printer_type = request.POST.get('printer_type', 'thermal')
        connection_type = request.POST.get('connection_type', 'usb')
        ip_address = request.POST.get('ip_address', '').strip()
        port = request.POST.get('port', '').strip()
        location = request.POST.get('location', '').strip()
        department = request.POST.get('department', '').strip()
        is_active = request.POST.get('is_active') == 'on'
        default_for_receipts = request.POST.get('default_for_receipts') == 'on'

        # التحقق من صحة البيانات
        if not name:
            messages.error(request, 'يرجى ملء جميع الحقول المطلوبة!')
            return render(request, 'definitions/printer_form.html', {
                'action': 'edit',
                'printer': printer,
                'form_data': request.POST
            })

        try:
            # إذا كانت الطابعة افتراضية للإيصالات، إلغاء الافتراضية من الطابعات الأخرى
            if default_for_receipts:
                PrinterDefinition.objects.filter(default_for_receipts=True).exclude(id=printer_id).update(default_for_receipts=False)

            # تحديث الطابعة
            printer.name = name
            printer.description = description or ''
            printer.printer_type = printer_type
            printer.connection_type = connection_type
            printer.ip_address = ip_address or None
            printer.port = int(port) if port else None
            printer.location = location or ''
            printer.department = department or ''
            printer.default_for_receipts = default_for_receipts
            printer.is_active = is_active
            printer.save()

            messages.success(request, f'تم تحديث الطابعة "{printer.name}" بنجاح!')
            return redirect('definitions:printer_detail', printer_id=printer.id)

        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء تحديث الطابعة: {str(e)}')

    context = {
        'action': 'edit',
        'printer': printer
    }
    return render(request, 'definitions/printer_form.html', context)

@login_required
def printer_delete(request, printer_id):
    """حذف الطابعة"""
    printer = get_object_or_404(PrinterDefinition, id=printer_id)

    if request.method == 'POST':
        printer_name = printer.name
        printer.delete()
        messages.success(request, f'تم حذف الطابعة "{printer_name}" بنجاح!')
        return redirect('definitions:printer_list')

    context = {'printer': printer}
    return render(request, 'definitions/printer_detail.html', context)

@login_required
def printer_quick_delete(request, printer_id):
    """حذف سريع للطابعة"""
    if request.method == 'POST':
        printer = get_object_or_404(PrinterDefinition, id=printer_id)
        printer_name = printer.name
        printer.delete()
        messages.success(request, f'تم حذف الطابعة "{printer_name}" بنجاح!')

    return redirect('definitions:printer_list')
