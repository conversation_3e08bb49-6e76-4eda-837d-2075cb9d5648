{% extends 'base.html' %}
{% load static %}

{% block title %}لوحة تحكم التصنيع{% endblock %}

{% block extra_css %}
<style>
    /* Manufacturing Dashboard Styles */
    :root {
        --manufacturing-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --manufacturing-success: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        --manufacturing-warning: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        --manufacturing-danger: linear-gradient(135deg, #fc466b 0%, #3f5efb 100%);
        --manufacturing-info: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .manufacturing-header {
        background: var(--manufacturing-primary);
        padding: 3rem 0;
        margin-bottom: 3rem;
        position: relative;
        overflow: visible;
        color: white;
    }

    .manufacturing-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="manufacturing-pattern" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23manufacturing-pattern)"/></svg>');
        opacity: 0.3;
    }

    .header-content {
        position: relative;
        z-index: 2;
        display: flex;
        align-items: center;
    }

    .header-icon {
        width: 80px;
        height: 80px;
        background: rgba(255,255,255,0.2);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255,255,255,0.3);
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 2rem;
        font-size: 2rem;
        color: white;
    }

    .header-text h1 {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .header-text p {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-bottom: 0;
    }

    /* Stats Cards */
    .stats-card-manufacturing {
        background: white;
        border-radius: 20px;
        box-shadow: 0 8px 30px rgba(0,0,0,0.08);
        transition: all 0.4s ease;
        overflow: hidden;
        border: 1px solid rgba(0,0,0,0.05);
        position: relative;
        height: 100%;
    }

    .stats-card-manufacturing:hover {
        transform: translateY(-8px);
        box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    }

    .stats-card-manufacturing::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--manufacturing-primary);
    }

    .stats-card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 2rem 2rem 1rem;
    }

    .stats-icon-manufacturing {
        width: 60px;
        height: 60px;
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
        position: relative;
        overflow: hidden;
    }

    .stats-icon-manufacturing::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%);
        animation: manufacturing-shimmer 3s infinite;
    }

    @keyframes manufacturing-shimmer {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .stats-card-body {
        padding: 0 2rem 2rem;
    }

    .stats-number {
        font-size: 2.5rem;
        font-weight: 700;
        color: #2d3748;
        margin-bottom: 0.5rem;
        line-height: 1;
    }

    .stats-label {
        font-size: 0.95rem;
        color: #718096;
        font-weight: 500;
        margin-bottom: 1rem;
    }

    .stats-link {
        color: #667eea;
        text-decoration: none;
        font-size: 0.9rem;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .stats-link:hover {
        color: #764ba2;
        transform: translateX(-3px);
    }

    /* Quick Actions */
    .quick-actions-manufacturing {
        background: white;
        border-radius: 20px;
        box-shadow: 0 8px 30px rgba(0,0,0,0.08);
        padding: 2rem;
        margin-bottom: 2rem;
    }

    .action-btn-manufacturing {
        background: var(--manufacturing-primary);
        border: none;
        color: white;
        border-radius: 12px;
        padding: 0.75rem 1.5rem;
        font-weight: 500;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        margin: 0.25rem;
    }

    .action-btn-manufacturing:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        color: white;
    }

    /* Recent Orders Table */
    .recent-orders-table {
        background: white;
        border-radius: 20px;
        box-shadow: 0 8px 30px rgba(0,0,0,0.08);
        overflow: hidden;
    }

    .table-header {
        background: #f8f9fa;
        padding: 1.5rem 2rem;
        border-bottom: 1px solid #e9ecef;
    }

    .table-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 0;
    }

    .custom-table {
        margin-bottom: 0;
    }

    .custom-table th {
        background: #f8f9fa;
        border: none;
        font-weight: 600;
        color: #495057;
        padding: 1rem 1.5rem;
    }

    .custom-table td {
        border: none;
        padding: 1rem 1.5rem;
        vertical-align: middle;
    }

    .custom-table tbody tr {
        border-bottom: 1px solid #f1f3f4;
        transition: background-color 0.3s ease;
    }

    .custom-table tbody tr:hover {
        background-color: #f8f9fa;
    }

    .status-badge {
        padding: 0.375rem 0.75rem;
        border-radius: 8px;
        font-size: 0.8rem;
        font-weight: 500;
    }

    .status-draft { background: #e3f2fd; color: #1976d2; }
    .status-confirmed { background: #e8f5e8; color: #2e7d32; }
    .status-in_progress { background: #fff3e0; color: #f57c00; }
    .status-completed { background: #e8f5e8; color: #388e3c; }
    .status-cancelled { background: #ffebee; color: #d32f2f; }

    .priority-badge {
        padding: 0.25rem 0.5rem;
        border-radius: 6px;
        font-size: 0.75rem;
        font-weight: 500;
    }

    .priority-low { background: #e8f5e8; color: #2e7d32; }
    .priority-normal { background: #e3f2fd; color: #1976d2; }
    .priority-high { background: #fff3e0; color: #f57c00; }
    .priority-urgent { background: #ffebee; color: #d32f2f; }

    /* Responsive */
    @media (max-width: 768px) {
        .manufacturing-header {
            padding: 2rem 0;
        }
        
        .header-content {
            flex-direction: column;
            text-align: center;
        }
        
        .header-icon {
            margin-left: 0;
            margin-bottom: 1rem;
        }
        
        .header-text h1 {
            font-size: 2rem;
        }
        
        .stats-number {
            font-size: 2rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Manufacturing Header -->
<div class="manufacturing-header">
    <div class="container-fluid">
        <div class="header-content">
            <div class="header-icon">
                <i class="bi bi-gear-wide-connected"></i>
            </div>
            <div class="header-text">
                <h1>لوحة تحكم التصنيع</h1>
                <p>إدارة شاملة لعمليات الإنتاج والتصنيع</p>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="container-fluid mb-4">
    <div class="quick-actions-manufacturing">
        <h4 class="mb-3">{% if user_language == 'en' %}Quick Actions{% else %}الإجراءات السريعة{% endif %}</h4>
        <div class="d-flex flex-wrap">
            <a href="{% url 'manufacturing:production_order_create' %}" class="action-btn-manufacturing">
                <i class="bi bi-plus-circle me-2"></i>{% if user_language == 'en' %}New Production Order{% else %}أمر إنتاج جديد{% endif %}
            </a>
            <a href="{% url 'manufacturing:production_order_list' %}" class="action-btn-manufacturing">
                <i class="bi bi-list-ul me-2"></i>{% if user_language == 'en' %}View Production Orders{% else %}عرض أوامر الإنتاج{% endif %}
            </a>
            <button class="action-btn-manufacturing" onclick="refreshDashboard()">
                <i class="bi bi-arrow-clockwise me-2"></i>{% if user_language == 'en' %}Refresh Data{% else %}تحديث البيانات{% endif %}
            </button>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="container-fluid mb-4">
    <div class="row g-4">
        <!-- Total Orders -->
        <div class="col-xl-3 col-lg-6 col-md-6">
            <div class="stats-card-manufacturing">
                <div class="stats-card-header">
                    <div class="stats-icon-manufacturing" style="background: var(--manufacturing-primary);">
                        <i class="bi bi-clipboard-data"></i>
                    </div>
                </div>
                <div class="stats-card-body">
                    <div class="stats-number">{{ total_orders }}</div>
                    <div class="stats-label">إجمالي أوامر الإنتاج</div>
                    <a href="{% url 'manufacturing:production_order_list' %}" class="stats-link">
                        <i class="bi bi-arrow-left me-1"></i>عرض التفاصيل
                    </a>
                </div>
            </div>
        </div>

        <!-- Active Orders -->
        <div class="col-xl-3 col-lg-6 col-md-6">
            <div class="stats-card-manufacturing">
                <div class="stats-card-header">
                    <div class="stats-icon-manufacturing" style="background: var(--manufacturing-warning);">
                        <i class="bi bi-gear-fill"></i>
                    </div>
                </div>
                <div class="stats-card-body">
                    <div class="stats-number">{{ active_orders }}</div>
                    <div class="stats-label">أوامر قيد التنفيذ</div>
                    <a href="{% url 'manufacturing:production_order_list' %}?status=in_progress" class="stats-link">
                        <i class="bi bi-arrow-left me-1"></i>عرض التفاصيل
                    </a>
                </div>
            </div>
        </div>

        <!-- Completed Orders -->
        <div class="col-xl-3 col-lg-6 col-md-6">
            <div class="stats-card-manufacturing">
                <div class="stats-card-header">
                    <div class="stats-icon-manufacturing" style="background: var(--manufacturing-success);">
                        <i class="bi bi-check-circle-fill"></i>
                    </div>
                </div>
                <div class="stats-card-body">
                    <div class="stats-number">{{ completed_orders }}</div>
                    <div class="stats-label">أوامر مكتملة</div>
                    <a href="{% url 'manufacturing:production_order_list' %}?status=completed" class="stats-link">
                        <i class="bi bi-arrow-left me-1"></i>عرض التفاصيل
                    </a>
                </div>
            </div>
        </div>

        <!-- Pending Orders -->
        <div class="col-xl-3 col-lg-6 col-md-6">
            <div class="stats-card-manufacturing">
                <div class="stats-card-header">
                    <div class="stats-icon-manufacturing" style="background: var(--manufacturing-info);">
                        <i class="bi bi-clock-fill"></i>
                    </div>
                </div>
                <div class="stats-card-body">
                    <div class="stats-number">{{ pending_orders }}</div>
                    <div class="stats-label">أوامر في الانتظار</div>
                    <a href="{% url 'manufacturing:production_order_list' %}?status=draft" class="stats-link">
                        <i class="bi bi-arrow-left me-1"></i>عرض التفاصيل
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function refreshDashboard() {
    location.reload();
}
</script>
{% endblock %}
