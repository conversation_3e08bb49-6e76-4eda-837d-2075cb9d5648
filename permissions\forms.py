from django import forms
from django.contrib.auth.models import User
from django.contrib.auth.forms import UserCreationForm
from .models import Department, JobPosition, Permission, PermissionTemplate, UserProfile as PermissionsUserProfile
from system_settings.models import UserProfile


class UserProfileForm(forms.ModelForm):
    """نموذج تعديل ملف المستخدم للصلاحيات"""

    class Meta:
        model = PermissionsUserProfile
        fields = ['job_position', 'permissions', 'phone', 'avatar', 'is_active']
        widgets = {
            'job_position': forms.Select(attrs={
                'class': 'form-select',
                'placeholder': 'اختر المنصب الوظيفي'
            }),
            'permissions': forms.CheckboxSelectMultiple(attrs={
                'class': 'form-check-input'
            }),
            'phone': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'رقم الهاتف',
                'dir': 'ltr'
            }),
            'avatar': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': 'image/*'
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # تخصيص عرض الصلاحيات
        self.fields['permissions'].queryset = Permission.objects.filter(is_active=True).order_by('module', 'permission_type')

        # تخصيص عرض المناصب
        self.fields['job_position'].queryset = JobPosition.objects.filter(is_active=True).select_related('department')

        # إضافة تسميات مخصصة
        self.fields['job_position'].label = 'المنصب الوظيفي'
        self.fields['permissions'].label = 'الصلاحيات'
        self.fields['phone'].label = 'رقم الهاتف'
        self.fields['avatar'].label = 'الصورة الشخصية'
        self.fields['is_active'].label = 'نشط'


class PermissionTemplateForm(forms.ModelForm):
    """نموذج إنشاء قالب صلاحيات"""
    
    class Meta:
        model = PermissionTemplate
        fields = ['name', 'job_position', 'permissions', 'description', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'اسم القالب'
            }),
            'job_position': forms.Select(attrs={
                'class': 'form-select',
                'placeholder': 'اختر المنصب الوظيفي'
            }),
            'permissions': forms.CheckboxSelectMultiple(attrs={
                'class': 'form-check-input'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'placeholder': 'وصف القالب',
                'rows': 3
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # تخصيص عرض الصلاحيات
        self.fields['permissions'].queryset = Permission.objects.filter(is_active=True).order_by('module', 'permission_type')
        
        # تخصيص عرض المناصب
        self.fields['job_position'].queryset = JobPosition.objects.filter(is_active=True).select_related('department')
        
        # إضافة تسميات مخصصة
        self.fields['name'].label = 'اسم القالب'
        self.fields['job_position'].label = 'المنصب الوظيفي'
        self.fields['permissions'].label = 'الصلاحيات'
        self.fields['description'].label = 'وصف القالب'
        self.fields['is_active'].label = 'نشط'


class DepartmentForm(forms.ModelForm):
    """نموذج إنشاء/تعديل قسم"""
    
    class Meta:
        model = Department
        fields = ['name', 'description', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'اسم القسم'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'placeholder': 'وصف القسم',
                'rows': 3
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # إضافة تسميات مخصصة
        self.fields['name'].label = 'اسم القسم'
        self.fields['description'].label = 'وصف القسم'
        self.fields['is_active'].label = 'نشط'


class JobPositionForm(forms.ModelForm):
    """نموذج إنشاء/تعديل منصب وظيفي"""
    
    class Meta:
        model = JobPosition
        fields = ['name', 'department', 'description', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'اسم المنصب'
            }),
            'department': forms.Select(attrs={
                'class': 'form-select',
                'placeholder': 'اختر القسم'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'placeholder': 'وصف المنصب',
                'rows': 3
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # تخصيص عرض الأقسام
        self.fields['department'].queryset = Department.objects.filter(is_active=True)
        
        # إضافة تسميات مخصصة
        self.fields['name'].label = 'اسم المنصب'
        self.fields['department'].label = 'القسم'
        self.fields['description'].label = 'وصف المنصب'
        self.fields['is_active'].label = 'نشط'


# تم نقل نماذج البحث إلى system_settings


# تم نقل نماذج إنشاء المستخدمين إلى system_settings
# استخدم system_settings.forms.UserCreateForm بدلاً من هذا النموذج
