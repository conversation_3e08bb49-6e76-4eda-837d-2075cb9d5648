{% extends 'base.html' %}
{% load static %}

{% block title %}إعدادات النظام المتقدمة{% endblock %}

{% block extra_css %}
<style>
    .settings-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 2rem;
    }

    .settings-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        border-radius: 15px;
        margin-bottom: 2rem;
        text-align: center;
    }

    .settings-card {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
    }

    .settings-section {
        margin-bottom: 2rem;
        padding-bottom: 2rem;
        border-bottom: 1px solid #eee;
    }

    .settings-section:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }

    .section-title {
        color: #667eea;
        font-size: 1.25rem;
        font-weight: 600;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 0.5rem;
    }

    .form-control {
        border-radius: 8px;
        border: 2px solid #e2e8f0;
        padding: 0.75rem;
        transition: all 0.3s ease;
    }

    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    .btn-save {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        border: none;
        padding: 0.75rem 2rem;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-save:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        color: white;
    }

    .form-check-input:checked {
        background-color: #667eea;
        border-color: #667eea;
    }

    .alert {
        border-radius: 10px;
        border: none;
    }
</style>
{% endblock %}

{% block content %}
<div class="settings-container">
    <!-- Header -->
    <div class="settings-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1><i class="bi bi-gear-wide-connected me-2"></i>إعدادات النظام المتقدمة</h1>
                <p class="mb-0">إدارة وتخصيص جميع إعدادات النظام والتطبيق</p>
            </div>
            <a href="{% url 'system_settings:dashboard' %}" class="btn btn-outline-light">
                <i class="bi bi-arrow-right me-2"></i>العودة
            </a>
        </div>
    </div>

    <!-- Messages -->
    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                <i class="bi bi-check-circle me-2"></i>{{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        {% endfor %}
    {% endif %}

    <!-- Settings Form -->
    <form method="post" enctype="multipart/form-data">
        {% csrf_token %}
        
        <div class="settings-card">
            <!-- معلومات الشركة -->
            <div class="settings-section">
                <h3 class="section-title">
                    <i class="bi bi-building"></i>
                    معلومات الشركة
                </h3>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">اسم الشركة</label>
                            <input type="text" name="company_name" class="form-control" 
                                   value="{{ settings.company_name }}" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">الرقم الضريبي</label>
                            <input type="text" name="company_tax_number" class="form-control" 
                                   value="{{ settings.company_tax_number }}">
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="form-label">هاتف الشركة</label>
                            <input type="text" name="company_phone" class="form-control"
                                   value="{{ settings.company_phone }}" placeholder="+20 ************">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="form-label">بريد الشركة</label>
                            <input type="email" name="company_email" class="form-control"
                                   value="{{ settings.company_email }}" placeholder="<EMAIL>">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="form-label">الدولة</label>
                            <select name="country" class="form-control">
                                {% for code, name in countries %}
                                    <option value="{{ code }}" {% if settings.country == code %}selected{% endif %}>
                                        {{ name }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">عنوان الشركة</label>
                    <textarea name="company_address" class="form-control" rows="3">{{ settings.company_address }}</textarea>
                </div>
                
                <div class="form-group">
                    <label class="form-label">موقع الشركة</label>
                    <input type="url" name="company_website" class="form-control" 
                           value="{{ settings.company_website }}">
                </div>
            </div>

            <!-- إعدادات النظام -->
            <div class="settings-section">
                <h3 class="section-title">
                    <i class="bi bi-sliders"></i>
                    إعدادات النظام العامة
                </h3>
                
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="form-label">لغة النظام</label>
                            <select name="system_language" class="form-control">
                                {% for code, name in languages %}
                                    <option value="{{ code }}" {% if settings.system_language == code %}selected{% endif %}>
                                        {{ name }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="form-label">العملة</label>
                            <select name="currency_code" class="form-control">
                                {% for code, name in currencies %}
                                    <option value="{{ code }}" {% if settings.currency_code == code %}selected{% endif %}>
                                        {{ name }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="form-label">رمز العملة</label>
                            <input type="text" name="currency_symbol" class="form-control"
                                   value="{{ settings.currency_symbol }}" placeholder="ج.م">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="form-label">المنطقة الزمنية</label>
                            <select name="system_timezone" class="form-control">
                                {% for code, name in timezones %}
                                    <option value="{{ code }}" {% if settings.system_timezone == code %}selected{% endif %}>
                                        {{ name }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="form-label">تنسيق التاريخ</label>
                            <select name="date_format" class="form-control">
                                <option value="d/m/Y" {% if settings.date_format == 'd/m/Y' %}selected{% endif %}>DD/MM/YYYY</option>
                                <option value="m/d/Y" {% if settings.date_format == 'm/d/Y' %}selected{% endif %}>MM/DD/YYYY</option>
                                <option value="Y-m-d" {% if settings.date_format == 'Y-m-d' %}selected{% endif %}>YYYY-MM-DD</option>
                                <option value="d-m-Y" {% if settings.date_format == 'd-m-Y' %}selected{% endif %}>DD-MM-YYYY</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="form-label">تنسيق الوقت</label>
                            <select name="time_format" class="form-control">
                                <option value="24" {% if settings.time_format == '24' %}selected{% endif %}>24 ساعة</option>
                                <option value="12" {% if settings.time_format == '12' %}selected{% endif %}>12 ساعة</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="form-label">الخانات العشرية</label>
                            <input type="number" name="decimal_places" class="form-control"
                                   value="{{ settings.decimal_places }}" min="0" max="6">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="form-label">فاصل الآلاف</label>
                            <input type="text" name="thousand_separator" class="form-control"
                                   value="{{ settings.thousand_separator }}" maxlength="1">
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="form-label">اللون الأساسي</label>
                            <input type="color" name="theme_color" class="form-control"
                                   value="{{ settings.theme_color }}" style="height: 50px;">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="form-label">اللون الثانوي</label>
                            <input type="color" name="secondary_color" class="form-control"
                                   value="{{ settings.secondary_color }}" style="height: 50px;">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="form-label">وضع الواجهة</label>
                            <select name="theme_mode" class="form-control">
                                <option value="light" {% if settings.theme_mode == 'light' %}selected{% endif %}>فاتح</option>
                                <option value="dark" {% if settings.theme_mode == 'dark' %}selected{% endif %}>داكن</option>
                                <option value="auto" {% if settings.theme_mode == 'auto' %}selected{% endif %}>تلقائي</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="form-label">عدد العناصر في الصفحة</label>
                            <input type="number" name="items_per_page" class="form-control"
                                   value="{{ settings.items_per_page }}" min="10" max="100">
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-3">
                        <div class="form-check mb-3">
                            <input type="checkbox" name="show_breadcrumbs" class="form-check-input"
                                   {% if settings.show_breadcrumbs %}checked{% endif %}>
                            <label class="form-check-label">إظهار مسار التنقل</label>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-check mb-3">
                            <input type="checkbox" name="enable_animations" class="form-check-input"
                                   {% if settings.enable_animations %}checked{% endif %}>
                            <label class="form-check-label">تفعيل الرسوم المتحركة</label>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-check mb-3">
                            <input type="checkbox" name="compact_mode" class="form-check-input"
                                   {% if settings.compact_mode %}checked{% endif %}>
                            <label class="form-check-label">الوضع المضغوط</label>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-check mb-3">
                            <input type="checkbox" name="sidebar_collapsed" class="form-check-input"
                                   {% if settings.sidebar_collapsed %}checked{% endif %}>
                            <label class="form-check-label">القائمة مطوية افتراضياً</label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- إعدادات الأمان -->
            <div class="settings-section">
                <h3 class="section-title">
                    <i class="bi bi-shield-check"></i>
                    إعدادات الأمان
                </h3>
                
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="form-label">انتهاء الجلسة (دقيقة)</label>
                            <input type="number" name="session_timeout" class="form-control"
                                   value="{{ settings.session_timeout }}" min="5" max="480">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="form-label">الحد الأدنى لطول كلمة المرور</label>
                            <input type="number" name="password_min_length" class="form-control"
                                   value="{{ settings.password_min_length }}" min="6" max="20">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="form-label">عدد محاولات تسجيل الدخول</label>
                            <input type="number" name="max_login_attempts" class="form-control"
                                   value="{{ settings.max_login_attempts }}" min="3" max="10">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="form-label">مدة الحظر (دقيقة)</label>
                            <input type="number" name="lockout_duration" class="form-control"
                                   value="{{ settings.lockout_duration }}" min="5" max="120">
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">انتهاء كلمة المرور (يوم)</label>
                            <input type="number" name="password_expiry_days" class="form-control"
                                   value="{{ settings.password_expiry_days }}" min="30" max="365">
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-3">
                        <div class="form-check mb-3">
                            <input type="checkbox" name="require_strong_password" class="form-check-input"
                                   {% if settings.require_strong_password %}checked{% endif %}>
                            <label class="form-check-label">كلمة مرور قوية مطلوبة</label>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-check mb-3">
                            <input type="checkbox" name="require_2fa" class="form-check-input"
                                   {% if settings.require_2fa %}checked{% endif %}>
                            <label class="form-check-label">المصادقة الثنائية مطلوبة</label>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-check mb-3">
                            <input type="checkbox" name="auto_logout_inactive" class="form-check-input"
                                   {% if settings.auto_logout_inactive %}checked{% endif %}>
                            <label class="form-check-label">تسجيل خروج تلقائي</label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- إعدادات البريد الإلكتروني -->
            <div class="settings-section">
                <h3 class="section-title">
                    <i class="bi bi-envelope"></i>
                    إعدادات البريد الإلكتروني
                </h3>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">خادم البريد الإلكتروني</label>
                            <input type="text" name="email_host" class="form-control"
                                   value="{{ settings.email_host }}" placeholder="smtp.gmail.com">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="form-label">المنفذ</label>
                            <input type="number" name="email_port" class="form-control"
                                   value="{{ settings.email_port }}" min="1" max="65535">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="form-label">اسم المرسل</label>
                            <input type="text" name="email_from_name" class="form-control"
                                   value="{{ settings.email_from_name }}">
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">اسم مستخدم البريد</label>
                            <input type="text" name="email_host_user" class="form-control"
                                   value="{{ settings.email_host_user }}">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">كلمة مرور البريد</label>
                            <input type="password" name="email_host_password" class="form-control"
                                   value="{{ settings.email_host_password }}">
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">توقيع البريد الإلكتروني</label>
                    <textarea name="email_signature" class="form-control" rows="3">{{ settings.email_signature }}</textarea>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-check mb-3">
                            <input type="checkbox" name="email_use_tls" class="form-check-input"
                                   {% if settings.email_use_tls %}checked{% endif %}>
                            <label class="form-check-label">استخدام TLS</label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check mb-3">
                            <input type="checkbox" name="email_use_ssl" class="form-check-input"
                                   {% if settings.email_use_ssl %}checked{% endif %}>
                            <label class="form-check-label">استخدام SSL</label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- إعدادات الإشعارات -->
            <div class="settings-section">
                <h3 class="section-title">
                    <i class="bi bi-bell"></i>
                    إعدادات الإشعارات
                </h3>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">تكرار الإشعارات</label>
                            <select name="notification_frequency" class="form-control">
                                <option value="instant" {% if settings.notification_frequency == 'instant' %}selected{% endif %}>فوري</option>
                                <option value="hourly" {% if settings.notification_frequency == 'hourly' %}selected{% endif %}>كل ساعة</option>
                                <option value="daily" {% if settings.notification_frequency == 'daily' %}selected{% endif %}>يومي</option>
                                <option value="weekly" {% if settings.notification_frequency == 'weekly' %}selected{% endif %}>أسبوعي</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-3">
                        <div class="form-check mb-3">
                            <input type="checkbox" name="notifications_enabled" class="form-check-input"
                                   {% if settings.notifications_enabled %}checked{% endif %}>
                            <label class="form-check-label">تفعيل الإشعارات</label>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-check mb-3">
                            <input type="checkbox" name="email_notifications" class="form-check-input"
                                   {% if settings.email_notifications %}checked{% endif %}>
                            <label class="form-check-label">إشعارات البريد الإلكتروني</label>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-check mb-3">
                            <input type="checkbox" name="sms_notifications" class="form-check-input"
                                   {% if settings.sms_notifications %}checked{% endif %}>
                            <label class="form-check-label">الرسائل النصية</label>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-check mb-3">
                            <input type="checkbox" name="push_notifications" class="form-check-input"
                                   {% if settings.push_notifications %}checked{% endif %}>
                            <label class="form-check-label">الإشعارات الفورية</label>
                        </div>
                    </div>
                </div>

                <div class="form-check mb-3">
                    <input type="checkbox" name="notification_sound" class="form-check-input"
                           {% if settings.notification_sound %}checked{% endif %}>
                    <label class="form-check-label">صوت الإشعارات</label>
                </div>
            </div>
            <!-- إعدادات النسخ الاحتياطي -->
            <div class="settings-section">
                <h3 class="section-title">
                    <i class="bi bi-cloud-arrow-up"></i>
                    إعدادات النسخ الاحتياطي
                </h3>

                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="form-label">تكرار النسخ الاحتياطي</label>
                            <select name="backup_frequency" class="form-control">
                                <option value="hourly" {% if settings.backup_frequency == 'hourly' %}selected{% endif %}>كل ساعة</option>
                                <option value="daily" {% if settings.backup_frequency == 'daily' %}selected{% endif %}>يومي</option>
                                <option value="weekly" {% if settings.backup_frequency == 'weekly' %}selected{% endif %}>أسبوعي</option>
                                <option value="monthly" {% if settings.backup_frequency == 'monthly' %}selected{% endif %}>شهري</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="form-label">موقع النسخ الاحتياطي</label>
                            <select name="backup_location" class="form-control">
                                <option value="local" {% if settings.backup_location == 'local' %}selected{% endif %}>محلي</option>
                                <option value="cloud" {% if settings.backup_location == 'cloud' %}selected{% endif %}>سحابي</option>
                                <option value="ftp" {% if settings.backup_location == 'ftp' %}selected{% endif %}>FTP</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="form-label">مدة الاحتفاظ (يوم)</label>
                            <input type="number" name="backup_retention_days" class="form-control"
                                   value="{{ settings.backup_retention_days }}" min="7" max="365">
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4">
                        <div class="form-check mb-3">
                            <input type="checkbox" name="backup_enabled" class="form-check-input"
                                   {% if settings.backup_enabled %}checked{% endif %}>
                            <label class="form-check-label">تفعيل النسخ الاحتياطي</label>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-check mb-3">
                            <input type="checkbox" name="backup_compress" class="form-check-input"
                                   {% if settings.backup_compress %}checked{% endif %}>
                            <label class="form-check-label">ضغط النسخ الاحتياطي</label>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-check mb-3">
                            <input type="checkbox" name="backup_encrypt" class="form-check-input"
                                   {% if settings.backup_encrypt %}checked{% endif %}>
                            <label class="form-check-label">تشفير النسخ الاحتياطي</label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- إعدادات التقارير -->
            <div class="settings-section">
                <h3 class="section-title">
                    <i class="bi bi-file-earmark-text"></i>
                    إعدادات التقارير
                </h3>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">تنسيق التقرير الافتراضي</label>
                            <select name="default_report_format" class="form-control">
                                <option value="pdf" {% if settings.default_report_format == 'pdf' %}selected{% endif %}>PDF</option>
                                <option value="excel" {% if settings.default_report_format == 'excel' %}selected{% endif %}>Excel</option>
                                <option value="csv" {% if settings.default_report_format == 'csv' %}selected{% endif %}>CSV</option>
                                <option value="html" {% if settings.default_report_format == 'html' %}selected{% endif %}>HTML</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">مدة الاحتفاظ بالتقارير (يوم)</label>
                            <input type="number" name="report_retention_days" class="form-control"
                                   value="{{ settings.report_retention_days }}" min="30" max="365">
                        </div>
                    </div>
                </div>

                <div class="form-check mb-3">
                    <input type="checkbox" name="auto_generate_reports" class="form-check-input"
                           {% if settings.auto_generate_reports %}checked{% endif %}>
                    <label class="form-check-label">إنشاء التقارير تلقائياً</label>
                </div>
            </div>

            <!-- إعدادات الأداء -->
            <div class="settings-section">
                <h3 class="section-title">
                    <i class="bi bi-speedometer2"></i>
                    إعدادات الأداء
                </h3>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">مهلة التخزين المؤقت (ثانية)</label>
                            <input type="number" name="cache_timeout" class="form-control"
                                   value="{{ settings.cache_timeout }}" min="60" max="3600">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">الحد الأقصى لحجم الملف (MB)</label>
                            <input type="number" name="max_file_upload_size" class="form-control"
                                   value="{{ settings.max_file_upload_size }}" min="1" max="100">
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-check mb-3">
                            <input type="checkbox" name="enable_caching" class="form-check-input"
                                   {% if settings.enable_caching %}checked{% endif %}>
                            <label class="form-check-label">تفعيل التخزين المؤقت</label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check mb-3">
                            <input type="checkbox" name="enable_compression" class="form-check-input"
                                   {% if settings.enable_compression %}checked{% endif %}>
                            <label class="form-check-label">تفعيل الضغط</label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- إعدادات مخصصة -->
            <div class="settings-section">
                <h3 class="section-title">
                    <i class="bi bi-code-slash"></i>
                    إعدادات مخصصة
                </h3>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">CSS مخصص</label>
                            <textarea name="custom_css" class="form-control" rows="5"
                                      placeholder="/* أضف CSS مخصص هنا */">{{ settings.custom_css }}</textarea>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">JavaScript مخصص</label>
                            <textarea name="custom_js" class="form-control" rows="5"
                                      placeholder="// أضف JavaScript مخصص هنا">{{ settings.custom_js }}</textarea>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Save Button -->
        <div class="text-center">
            <button type="submit" class="btn btn-save">
                <i class="bi bi-check-circle me-2"></i>
                حفظ جميع الإعدادات
            </button>
            <a href="{% url 'system_settings:dashboard' %}" class="btn btn-outline-secondary ms-3">
                <i class="bi bi-arrow-right me-2"></i>
                إلغاء والعودة
            </a>
        </div>
    </form>
</div>

<script>
// تطبيق اللغة فوراً عند التغيير
document.querySelector('select[name="system_language"]').addEventListener('change', function() {
    const form = this.closest('form');
    const formData = new FormData(form);

    fetch(window.location.href, {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    }).then(response => {
        if (response.ok) {
            window.location.reload();
        }
    });
});

// تحديث معاينة الألوان
document.querySelectorAll('input[type="color"]').forEach(input => {
    input.addEventListener('change', function() {
        document.documentElement.style.setProperty('--primary-color', this.value);
    });
});
</script>
{% endblock %}
