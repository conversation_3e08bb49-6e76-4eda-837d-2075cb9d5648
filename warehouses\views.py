from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from definitions.models import WarehouseDefinition, ProductDefinition

@login_required
def warehouses_dashboard(request):
    """لوحة تحكم المخازن"""
    # إحصائيات أساسية
    total_warehouses = WarehouseDefinition.objects.filter(is_active=True).count()
    total_products = ProductDefinition.objects.filter(is_active=True).count()

    # المخازن النشطة
    warehouses = WarehouseDefinition.objects.filter(is_active=True)[:10]

    context = {
        'total_warehouses': total_warehouses,
        'total_products': total_products,
        'warehouses': warehouses,
        'page_title': 'إدارة المخازن',
        'page_title_en': 'Warehouse Management',
    }

    return render(request, 'warehouses/dashboard.html', context)
