{% extends 'base.html' %}
{% load static %}

{% block title %}الإعدادات - أوساريك{% endblock %}

{% block extra_css %}
<style>
    .settings-container {
        max-width: 900px;
        margin: 0 auto;
        padding: 20px;
    }

    .settings-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px;
        border-radius: 15px;
        text-align: center;
        margin-bottom: 30px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    }

    .settings-title {
        font-size: 28px;
        font-weight: bold;
        margin-bottom: 10px;
    }

    .settings-card {
        background: white;
        border-radius: 15px;
        padding: 30px;
        margin-bottom: 20px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    }

    .card-title {
        font-size: 20px;
        font-weight: bold;
        color: #333;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        gap: 10px;
        border-bottom: 2px solid #f0f0f0;
        padding-bottom: 15px;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-label {
        display: block;
        font-weight: 600;
        color: #555;
        margin-bottom: 8px;
    }

    .form-control {
        width: 100%;
        padding: 12px 15px;
        border: 2px solid #e0e0e0;
        border-radius: 8px;
        font-size: 14px;
        transition: all 0.3s ease;
    }

    .form-control:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    .form-check {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 15px;
    }

    .form-check input[type="checkbox"] {
        width: 18px;
        height: 18px;
        cursor: pointer;
    }

    .form-check label {
        cursor: pointer;
        margin: 0;
    }

    .btn-save {
        background: #28a745;
        color: white;
        padding: 10px 20px;
        border: none;
        border-radius: 5px;
        font-size: 14px;
        cursor: pointer;
    }

    .btn-save:hover {
        background: #218838;
    }

    .btn-back {
        background: #6c757d;
        color: white;
        padding: 10px 20px;
        border: none;
        border-radius: 8px;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 20px;
        transition: all 0.3s ease;
    }

    .btn-back:hover {
        background: #5a6268;
        color: white;
        text-decoration: none;
        transform: translateY(-1px);
    }

    .alert {
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 20px;
    }

    .alert-success {
        background: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
    }

    .alert-error {
        background: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
    }
</style>
{% endblock %}

{% block content %}
<div class="settings-container">
    <!-- زر العودة -->
    <a href="{% url 'dashboard_home' %}" class="btn-back">
        <i class="bi bi-arrow-right"></i>
        {% if user_language == 'en' %}Back to Dashboard{% else %}العودة للوحة التحكم{% endif %}
    </a>

    <!-- رأس الإعدادات -->
    <div class="settings-header">
        <div class="settings-title">
            <i class="bi bi-gear-wide-connected"></i>
            {% if user_language == 'en' %}System Settings{% else %}إعدادات النظام{% endif %}
        </div>
        <p>{% if user_language == 'en' %}Customize your experience and manage your account settings{% else %}قم بتخصيص تجربتك وإدارة إعدادات حسابك{% endif %}</p>
    </div>

    <!-- عرض الرسائل -->
    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }}">
                <i class="bi bi-check-circle"></i>
                {{ message }}
            </div>
        {% endfor %}
    {% endif %}

    <!-- إعدادات الإشعارات -->
    <div class="settings-card">
        <div class="card-title">
            <i class="bi bi-bell"></i>
            {% if user_language == 'en' %}Notification Settings{% else %}إعدادات الإشعارات{% endif %}
        </div>

        <form method="post" action="{% url 'settings' %}">
            {% csrf_token %}
            <input type="hidden" name="setting_type" value="notifications">

            <div class="form-group">
                <div class="form-check">
                    <input type="checkbox" name="email_notifications" id="email_notifications" {% if settings.email_notifications %}checked{% endif %}>
                    <label for="email_notifications" class="form-label">
                        <strong>{% if user_language == 'en' %}Email Notifications{% else %}إشعارات البريد الإلكتروني{% endif %}</strong><br>
                        <small>{% if user_language == 'en' %}Receive important notifications via email{% else %}استقبال الإشعارات المهمة عبر البريد الإلكتروني{% endif %}</small>
                    </label>
                </div>
            </div>

            <div class="form-group">
                <div class="form-check">
                    <input type="checkbox" name="daily_reports" id="daily_reports" {% if settings.daily_reports %}checked{% endif %}>
                    <label for="daily_reports" class="form-label">
                        <strong>{% if user_language == 'en' %}Daily Reports{% else %}التقارير اليومية{% endif %}</strong><br>
                        <small>{% if user_language == 'en' %}Receive daily report with key activities{% else %}استقبال تقرير يومي بأهم الأنشطة{% endif %}</small>
                    </label>
                </div>
            </div>

            <div class="form-group">
                <div class="form-check">
                    <input type="checkbox" name="push_notifications" id="push_notifications" {% if settings.push_notifications %}checked{% endif %}>
                    <label for="push_notifications" class="form-label">
                        <strong>{% if user_language == 'en' %}Push Notifications{% else %}الإشعارات المنبثقة{% endif %}</strong><br>
                        <small>{% if user_language == 'en' %}Display notifications in browser{% else %}عرض الإشعارات في المتصفح{% endif %}</small>
                    </label>
                </div>
            </div>

            <div class="form-group">
                <div class="form-check">
                    <input type="checkbox" name="sound_notifications" id="sound_notifications" {% if settings.sound_notifications %}checked{% endif %}>
                    <label for="sound_notifications" class="form-label">
                        <strong>{% if user_language == 'en' %}Sound Notifications{% else %}الأصوات{% endif %}</strong><br>
                        <small>{% if user_language == 'en' %}Play sound when new notification arrives{% else %}تشغيل صوت عند وصول إشعار جديد{% endif %}</small>
                    </label>
                </div>
            </div>

            <input type="submit" value="{% if user_language == 'en' %}Save{% else %}حفظ{% endif %}" class="btn-save">
        </form>
    </div>

    <!-- إعدادات المظهر -->
    <div class="settings-card">
        <div class="card-title">
            <i class="bi bi-palette"></i>
            {% if user_language == 'en' %}Appearance Settings{% else %}إعدادات المظهر{% endif %}
            <small style="color: #666; font-weight: normal;">
                {% if user_language == 'en' %}
                    (Current:
                    {% if settings.dark_mode %}Dark Mode{% else %}Light Mode{% endif %},
                    Font {{ settings.font_size|title }},
                    {% if settings.language == 'en' %}English{% else %}Arabic{% endif %})
                {% else %}
                    (الحالي:
                    {% if settings.dark_mode %}وضع ليلي{% else %}وضع عادي{% endif %},
                    خط {% if settings.font_size == 'small' %}صغير{% elif settings.font_size == 'large' %}كبير{% else %}متوسط{% endif %},
                    {% if settings.language == 'en' %}إنجليزي{% else %}عربي{% endif %})
                {% endif %}
            </small>
        </div>

        <form method="post" action="{% url 'settings' %}">
            {% csrf_token %}
            <input type="hidden" name="setting_type" value="appearance">

            <div class="form-group">
                <div class="form-check">
                    <input type="checkbox" name="dark_mode" id="dark_mode" {% if settings.dark_mode %}checked{% endif %}>
                    <label for="dark_mode" class="form-label">
                        <strong>{% if user_language == 'en' %}Dark Mode{% else %}الوضع الليلي{% endif %}</strong><br>
                        <small>{% if user_language == 'en' %}Enable dark mode for eye comfort{% else %}تفعيل الوضع الليلي لراحة العينين{% endif %}</small>
                    </label>
                </div>
            </div>

            <div class="form-group">
                <label for="font_size" class="form-label">{% if user_language == 'en' %}Font Size{% else %}حجم الخط{% endif %}</label>
                <select class="form-control" name="font_size" id="font_size">
                    <option value="small" {% if settings.font_size == 'small' %}selected{% endif %}>{% if user_language == 'en' %}Small{% else %}صغير{% endif %}</option>
                    <option value="medium" {% if settings.font_size == 'medium' %}selected{% endif %}>{% if user_language == 'en' %}Medium{% else %}متوسط{% endif %}</option>
                    <option value="large" {% if settings.font_size == 'large' %}selected{% endif %}>{% if user_language == 'en' %}Large{% else %}كبير{% endif %}</option>
                </select>
            </div>

            <div class="form-group">
                <label for="language" class="form-label">{% if user_language == 'en' %}Interface Language{% else %}لغة الواجهة{% endif %}</label>
                <select class="form-control" name="language" id="language">
                    <option value="ar" {% if settings.language == 'ar' %}selected{% endif %}>العربية</option>
                    <option value="en" {% if settings.language == 'en' %}selected{% endif %}>English</option>
                </select>
            </div>

            <div class="form-group">
                <label for="timezone" class="form-label">المنطقة الزمنية</label>
                <select class="form-control" name="timezone" id="timezone">
                    <option value="Africa/Cairo" {% if settings.timezone == 'Africa/Cairo' %}selected{% endif %}>القاهرة (GMT+2)</option>
                    <option value="Asia/Riyadh" {% if settings.timezone == 'Asia/Riyadh' %}selected{% endif %}>الرياض (GMT+3)</option>
                    <option value="Asia/Dubai" {% if settings.timezone == 'Asia/Dubai' %}selected{% endif %}>دبي (GMT+4)</option>
                </select>
            </div>

            <input type="submit" value="{% if user_language == 'en' %}Save & Apply{% else %}حفظ وتطبيق{% endif %}" class="btn-save">
            <small style="color: #666; display: block; margin-top: 10px;">
                💡 {% if user_language == 'en' %}Changes will appear immediately after saving on all pages{% else %}ستظهر التغييرات فور الحفظ في جميع صفحات الموقع{% endif %}
            </small>
        </form>
    </div>

    <!-- إعدادات الأمان -->
    <div class="settings-card">
        <div class="card-title">
            <i class="bi bi-shield-check"></i>
            {% if user_language == 'en' %}Security Settings{% else %}إعدادات الأمان{% endif %}
        </div>

        <form method="post" action="{% url 'settings' %}">
            {% csrf_token %}
            <input type="hidden" name="setting_type" value="security">

            <div class="form-group">
                <div class="form-check">
                    <input type="checkbox" name="two_factor" id="two_factor" {% if settings.two_factor %}checked{% endif %}>
                    <label for="two_factor" class="form-label">
                        <strong>المصادقة الثنائية</strong><br>
                        <small>حماية إضافية لحسابك باستخدام رمز التحقق</small>
                    </label>
                </div>
            </div>

            <div class="form-group">
                <div class="form-check">
                    <input type="checkbox" name="login_alerts" id="login_alerts" {% if settings.login_alerts %}checked{% endif %}>
                    <label for="login_alerts" class="form-label">
                        <strong>تنبيهات تسجيل الدخول</strong><br>
                        <small>إشعار عند تسجيل الدخول من جهاز جديد</small>
                    </label>
                </div>
            </div>

            <div class="form-group">
                <div class="form-check">
                    <input type="checkbox" name="session_timeout" id="session_timeout" {% if settings.session_timeout %}checked{% endif %}>
                    <label for="session_timeout" class="form-label">
                        <strong>انتهاء الجلسة التلقائي</strong><br>
                        <small>تسجيل الخروج التلقائي بعد فترة عدم نشاط</small>
                    </label>
                </div>
            </div>

            <input type="submit" value="حفظ" class="btn-save">
        </form>
    </div>

    <!-- معلومات النظام -->
    <div class="settings-card">
        <div class="card-title">
            <i class="bi bi-info-circle"></i>
            معلومات النظام
        </div>

        <div class="form-group">
            <label class="form-label">إصدار النظام</label>
            <p style="margin: 0; color: #666;">أوساريك v1.0.0</p>
        </div>

        <div class="form-group">
            <label class="form-label">آخر تحديث</label>
            <p style="margin: 0; color: #666;">16 يوليو 2025</p>
        </div>

        <div class="form-group">
            <label class="form-label">حالة النظام</label>
            <p style="margin: 0; color: #28a745;">
                <i class="bi bi-check-circle"></i>
                يعمل بشكل طبيعي
            </p>
        </div>

        <div class="form-group">
            <label class="form-label">المستخدم الحالي</label>
            <p style="margin: 0; color: #666;">{{ user.get_full_name|default:user.username }}</p>
        </div>

        <div class="form-group">
            <label class="form-label">آخر تحديث للإعدادات</label>
            <p style="margin: 0; color: #666;">
                {% if settings.updated_at %}
                    {{ settings.updated_at|date:"d/m/Y H:i" }}
                {% else %}
                    لم يتم التحديث بعد
                {% endif %}
            </p>
        </div>
    </div>
</div>

<script>
// معاينة فورية لإعدادات المظهر
document.addEventListener('DOMContentLoaded', function() {
    // معاينة الوضع الليلي
    const darkModeCheckbox = document.getElementById('dark_mode');
    if (darkModeCheckbox) {
        darkModeCheckbox.addEventListener('change', function() {
            if (this.checked) {
                document.body.style.backgroundColor = '#1a1a1a';
                document.body.style.color = '#ffffff';
                document.querySelectorAll('.settings-card').forEach(card => {
                    card.style.backgroundColor = '#3c3c3c';
                    card.style.color = '#ffffff';
                });
            } else {
                document.body.style.backgroundColor = '';
                document.body.style.color = '';
                document.querySelectorAll('.settings-card').forEach(card => {
                    card.style.backgroundColor = '';
                    card.style.color = '';
                });
            }
        });
    }

    // معاينة حجم الخط
    const fontSizeSelect = document.getElementById('font_size');
    if (fontSizeSelect) {
        fontSizeSelect.addEventListener('change', function() {
            const size = this.value;
            applyFontSize(size);
        });
    }

    // معاينة الوضع الليلي مثل الإعدادات السريعة
    const darkModeCheckbox = document.getElementById('dark_mode');
    if (darkModeCheckbox) {
        darkModeCheckbox.addEventListener('change', function() {
            if (this.checked) {
                document.body.style.filter = 'invert(1) hue-rotate(180deg)';
                localStorage.setItem('darkMode', 'true');
            } else {
                document.body.style.filter = '';
                localStorage.setItem('darkMode', 'false');
            }
        });
    }
});

// دالة تطبيق حجم الخط
function applyFontSize(size) {
    const elements = document.querySelectorAll('*');
    elements.forEach(element => {
        if (size === 'small') {
            element.style.fontSize = '12px';
        } else if (size === 'large') {
            element.style.fontSize = '18px';
        } else {
            element.style.fontSize = '14px';
        }
    });

    // حفظ في localStorage
    localStorage.setItem('fontSize', size);
});
</script>
{% endblock %}
