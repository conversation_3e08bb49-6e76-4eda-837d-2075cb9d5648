from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib.admin.views.decorators import staff_member_required
from django.contrib.auth.models import User, Group, Permission
from django.contrib.contenttypes.models import ContentType
from django.http import JsonResponse
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q, Count
from django.views.decorators.http import require_POST
from django.utils import timezone
from .models import SystemSettings, UserProfile
import json


def is_admin(user):
    """التحقق من صلاحيات الإدارة"""
    return user.is_superuser or user.groups.filter(name='Administrators').exists()


@login_required
@user_passes_test(is_admin)
def advanced_settings_dashboard(request):
    """لوحة تحكم الإعدادات المتقدمة"""

    # إحصائيات سريعة
    stats = {
        'total_users': User.objects.count(),
        'active_users': User.objects.filter(is_active=True).count(),
        'inactive_users': User.objects.filter(is_active=False).count(),
        'total_roles': 0,  # سيتم تحديثها لاحقاً
        'active_roles': 0,
        'system_logs_today': 0,
        'enabled_modules': 0,
        'total_permissions': Permission.objects.count(),
    }

    # المستخدمين النشطين
    active_users = User.objects.filter(
        is_active=True,
        last_login__isnull=False
    ).order_by('-last_login')[:10]

    context = {
        'stats': stats,
        'recent_logs': [],  # فارغة مؤقتاً
        'active_users': active_users,
        'enabled_modules': [],
        'department_stats': [],
        'position_stats': [],
    }

    return render(request, 'settings/advanced_dashboard.html', context)


@login_required
@user_passes_test(is_admin)
def advanced_system_settings(request):
    """إعدادات النظام المتقدمة"""

    settings = SystemSettings.get_settings()

    if request.method == 'POST':
        # تحديث الإعدادات
        for field_name in request.POST:
            if hasattr(settings, field_name) and field_name != 'csrfmiddlewaretoken':
                field_value = request.POST.get(field_name)

                # معالجة الحقول المختلفة
                if field_name in ['backup_enabled', 'require_strong_password', 'email_use_tls',
                                'notifications_enabled', 'email_notifications', 'sidebar_collapsed']:
                    field_value = field_value == 'on'
                elif field_name in ['session_timeout', 'password_min_length', 'max_login_attempts',
                                  'email_port', 'items_per_page']:
                    try:
                        field_value = int(field_value)
                    except (ValueError, TypeError):
                        continue

                setattr(settings, field_name, field_value)

        settings.updated_by = request.user
        settings.save()

        messages.success(request, 'تم حفظ إعدادات النظام بنجاح')
        return redirect('settings:advanced_system_settings')

    context = {
        'settings': settings,
        'timezones': [
            ('Asia/Riyadh', 'الرياض'),
            ('Asia/Dubai', 'دبي'),
            ('Asia/Kuwait', 'الكويت'),
            ('Asia/Qatar', 'قطر'),
            ('Asia/Bahrain', 'البحرين'),
            ('UTC', 'التوقيت العالمي'),
        ],
        'languages': [
            ('ar', 'العربية'),
            ('en', 'English'),
        ],
        'currencies': [
            ('SAR', 'ريال سعودي'),
            ('AED', 'درهم إماراتي'),
            ('KWD', 'دينار كويتي'),
            ('QAR', 'ريال قطري'),
            ('BHD', 'دينار بحريني'),
            ('USD', 'دولار أمريكي'),
        ],
    }

    return render(request, 'settings/advanced_system_settings.html', context)


@login_required
@user_passes_test(is_admin)
def users_management_advanced(request):
    """إدارة المستخدمين المتقدمة"""

    search_query = request.GET.get('search', '')
    status_filter = request.GET.get('status', '')

    users = User.objects.all()

    # تطبيق الفلاتر
    if search_query:
        users = users.filter(
            Q(username__icontains=search_query) |
            Q(first_name__icontains=search_query) |
            Q(last_name__icontains=search_query) |
            Q(email__icontains=search_query)
        )

    if status_filter == 'active':
        users = users.filter(is_active=True)
    elif status_filter == 'inactive':
        users = users.filter(is_active=False)
    elif status_filter == 'staff':
        users = users.filter(is_staff=True)
    elif status_filter == 'superuser':
        users = users.filter(is_superuser=True)

    # ترتيب النتائج
    users = users.order_by('username')

    # التصفح
    paginator = Paginator(users, 25)
    page_number = request.GET.get('page')
    users_page = paginator.get_page(page_number)

    # البيانات للفلاتر
    departments = UserProfile.DEPARTMENTS
    positions = UserProfile.POSITIONS

    context = {
        'users': users_page,
        'search_query': search_query,
        'status_filter': status_filter,
        'departments': departments,
        'positions': positions,
    }

    return render(request, 'settings/users_management_advanced.html', context)


@login_required
@user_passes_test(is_admin)
def user_detail_advanced(request, user_id):
    """تفاصيل المستخدم المتقدمة"""

    user = get_object_or_404(User, id=user_id)
    profile, created = UserProfile.objects.get_or_create(user=user)

    if request.method == 'POST':
        action = request.POST.get('action')

        if action == 'update_profile':
            # تحديث الملف الشخصي
            for field in ['phone', 'mobile', 'address', 'department', 'position', 'employee_id']:
                if field in request.POST:
                    setattr(profile, field, request.POST.get(field))

            profile.save()

        elif action == 'update_account':
            # تحديث معلومات الحساب
            user.first_name = request.POST.get('first_name', '')
            user.last_name = request.POST.get('last_name', '')
            user.email = request.POST.get('email', '')
            user.is_active = request.POST.get('is_active') == 'on'
            user.is_staff = request.POST.get('is_staff') == 'on'
            user.is_superuser = request.POST.get('is_superuser') == 'on'
            user.save()

        elif action == 'reset_password':
            # إعادة تعيين كلمة المرور
            new_password = request.POST.get('new_password')
            if new_password:
                user.set_password(new_password)
                user.save()

        messages.success(request, 'تم تحديث ملف المستخدم بنجاح')
        return redirect('settings:user_detail_advanced', user_id=user.id)

    # إحصائيات المستخدم
    user_stats = {
        'last_login': user.last_login,
        'date_joined': user.date_joined,
        'failed_attempts': profile.failed_login_attempts,
    }

    context = {
        'user': user,
        'profile': profile,
        'user_roles': [],  # فارغة مؤقتاً
        'available_roles': [],
        'user_logs': [],
        'user_stats': user_stats,
        'departments': UserProfile.DEPARTMENTS,
        'positions': UserProfile.POSITIONS,
    }

    return render(request, 'settings/user_detail_advanced.html', context)
